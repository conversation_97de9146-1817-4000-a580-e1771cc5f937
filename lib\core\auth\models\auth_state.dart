import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide User;

import '../../../features/auth/domain/entities/user.dart';
import '../../errors/app_error.dart';
import 'mfa_models.dart';
import 'security_context.dart';

part 'auth_state.freezed.dart';

/// Unified authentication state following Context7 MCP best practices
///
/// This replaces all 19 duplicate authentication providers with a single
/// source of truth for authentication state management.
@freezed
abstract class AuthState with _$AuthState {
  /// Initial state when authentication system is starting up
  const factory AuthState.initial() = AuthInitial;

  /// Loading state during authentication operations
  const factory AuthState.loading({String? operation, double? progress}) = AuthLoading;

  /// Authenticated state with complete user context
  const factory AuthState.authenticated({
    required User user,
    required Session session,
    required SecurityContext securityContext,
    required DateTime lastActivity,
    @Default(false) bool mfaEnabled,
    List<MfaFactor>? enrolledFactors,
  }) = AuthAuthenticated;

  /// Unauthenticated state - user needs to sign in
  const factory AuthState.unauthenticated({String? reason, DateTime? lastSignOut}) = AuthUnauthenticated;

  /// MFA required state - user needs to complete MFA challenge
  const factory AuthState.mfaRequired({
    required String challengeId,
    required List<MfaFactor> availableFactors,
    required String userId,
    DateTime? challengeExpiry,
  }) = AuthMfaRequired;

  /// Error state with detailed error information
  const factory AuthState.error({required AppError error, String? operation, DateTime? timestamp, bool? canRetry}) =
      AuthError;

  /// Session expired state - requires re-authentication
  const factory AuthState.sessionExpired({required DateTime expiredAt, String? reason, bool? canRefresh}) =
      AuthSessionExpired;

  /// Account locked state - security measure
  const factory AuthState.accountLocked({required String reason, DateTime? unlockAt, List<String>? unlockMethods}) =
      AuthAccountLocked;
}

/// Authentication operation types for tracking
enum AuthOperation {
  /// User sign-in operation
  signIn,

  /// User registration operation
  signUp,

  /// User sign-out operation
  signOut,

  /// Token refresh operation
  refreshToken,

  /// Multi-factor authentication verification
  mfaVerification,

  /// Password reset operation
  passwordReset,

  /// Session validation operation
  sessionValidation,

  /// Device registration for authentication
  deviceRegistration,
}

/// Authentication result wrapper with detailed information
@freezed
class AuthResult<T> with _$AuthResult<T> {
  /// Creates a successful authentication result
  const factory AuthResult.success({required T data, String? message, Map<String, dynamic>? metadata}) = AuthSuccess<T>;

  /// Creates a failed authentication result
  const factory AuthResult.failure({required AppError error, String? operation, Map<String, dynamic>? context}) =
      AuthFailure<T>;

  /// Creates a result indicating MFA is required
  const factory AuthResult.mfaRequired({
    required String challengeId,
    required List<MfaFactor> availableFactors,
    Map<String, dynamic>? context,
  }) = AuthMfaRequiredResult<T>;
}

/// Authentication request models
@freezed
abstract class SignInRequest with _$SignInRequest {
  /// Creates a sign-in request
  const factory SignInRequest({
    required String email,
    required String password,
    String? deviceId,
    String? deviceName,
    Map<String, dynamic>? metadata,
  }) = _SignInRequest;
}

@freezed
abstract class SignUpRequest with _$SignUpRequest {
  /// Creates a sign-up request
  const factory SignUpRequest({
    required String email,
    required String password,
    String? fullName,
    String? phoneNumber,
    String? deviceId,
    String? deviceName,
    Map<String, dynamic>? metadata,
  }) = _SignUpRequest;
}

@freezed
abstract class PasswordResetRequest with _$PasswordResetRequest {
  /// Creates a password reset request
  const factory PasswordResetRequest({required String email, String? redirectUrl, Map<String, dynamic>? metadata}) =
      _PasswordResetRequest;
}

/// Authentication response models
@freezed
abstract class AuthResponse with _$AuthResponse {
  /// Creates an authentication response
  const factory AuthResponse({
    required User user,
    required Session session,
    required SecurityContext securityContext,
    bool? requiresMfa,
    String? mfaChallengeId,
    List<MfaFactor>? availableFactors,
  }) = _AuthResponse;
}

/// Token management models
@freezed
abstract class AuthTokens with _$AuthTokens {
  const factory AuthTokens({
    required String accessToken,
    required String refreshToken,
    required DateTime expiresAt,
    String? tokenType,
    List<String>? scopes,
  }) = _AuthTokens;
}

/// Session information
@freezed
abstract class SessionInfo with _$SessionInfo {
  const factory SessionInfo({
    required String sessionId,
    required String userId,
    required DateTime createdAt,
    required DateTime lastActivity,
    required DateTime expiresAt,
    String? deviceId,
    String? deviceName,
    String? ipAddress,
    String? userAgent,
    Map<String, dynamic>? metadata,
  }) = _SessionInfo;
}

/// Device trust information
@freezed
abstract class DeviceTrust with _$DeviceTrust {
  const factory DeviceTrust({
    required String deviceId,
    required String deviceFingerprint,
    required TrustLevel trustLevel,
    required DateTime lastSeen,
    DateTime? registeredAt,
    List<String>? capabilities,
    Map<String, dynamic>? metadata,
  }) = _DeviceTrust;
}

/// Trust levels for device authentication
enum TrustLevel {
  /// Trust level is unknown
  unknown,

  /// Device is not trusted
  untrusted,

  /// Basic trust level
  basic,

  /// Device is trusted
  trusted,

  /// Device is verified and highly trusted
  verified,
}

/// Assurance levels for authentication strength
enum AssuranceLevel {
  /// No assurance
  none,

  /// Low assurance level
  low,

  /// Medium assurance level
  medium,

  /// High assurance level
  high,

  /// Very high assurance level
  veryHigh,
}

/// Authentication event types for audit logging
enum AuthEventType {
  signInAttempt,
  signInSuccess,
  signInFailure,
  signUpAttempt,
  signUpSuccess,
  signUpFailure,
  signOut,
  tokenRefresh,
  mfaChallenge,
  mfaSuccess,
  mfaFailure,
  passwordReset,
  sessionExpired,
  accountLocked,
  deviceRegistered,
  securityAlert,
}

/// Authentication event for audit trail
@freezed
abstract class AuthEvent with _$AuthEvent {
  const factory AuthEvent({
    required String id,
    required AuthEventType type,
    required DateTime timestamp,
    String? userId,
    String? sessionId,
    String? deviceId,
    String? ipAddress,
    String? userAgent,
    Map<String, dynamic>? metadata,
    String? description,
  }) = _AuthEvent;
}

/// Extensions for AuthState
extension AuthStateExtensions on AuthState {
  /// Check if user is authenticated
  bool get isAuthenticated => when(
    initial: () => false,
    loading: (_, __) => false,
    authenticated: (_, __, ___, ____, _____, ______) => true,
    unauthenticated: (_, __) => false,
    mfaRequired: (_, __, ___, ____) => false,
    error: (_, __, ___, ____) => false,
    sessionExpired: (_, __, ___) => false,
    accountLocked: (_, __, ___) => false,
  );

  /// Check if authentication is in progress
  bool get isLoading => when(
    initial: () => false,
    loading: (_, __) => true,
    authenticated: (_, __, ___, ____, _____, ______) => false,
    unauthenticated: (_, __) => false,
    mfaRequired: (_, __, ___, ____) => false,
    error: (_, __, ___, ____) => false,
    sessionExpired: (_, __, ___) => false,
    accountLocked: (_, __, ___) => false,
  );

  /// Get current user if authenticated
  User? get currentUser => whenOrNull(authenticated: (user, _, __, ___, ____, _____) => user);

  /// Get current session if authenticated
  Session? get currentSession => whenOrNull(authenticated: (_, session, __, ___, ____, _____) => session);

  /// Get security context if authenticated
  SecurityContext? get securityContext =>
      whenOrNull(authenticated: (_, __, securityContext, ___, ____, _____) => securityContext);

  /// Check if MFA is enabled
  bool get isMfaEnabled => whenOrNull(authenticated: (_, __, ___, ____, mfaEnabled, _____) => mfaEnabled) ?? false;

  /// Check if MFA is required
  bool get requiresMfa => when(
    initial: () => false,
    loading: (_, __) => false,
    authenticated: (_, __, ___, ____, _____, ______) => false,
    unauthenticated: (_, __) => false,
    mfaRequired: (_, __, ___, ____) => true,
    error: (_, __, ___, ____) => false,
    sessionExpired: (_, __, ___) => false,
    accountLocked: (_, __, ___) => false,
  );

  /// Check if there's an error
  bool get hasError => when(
    initial: () => false,
    loading: (_, __) => false,
    authenticated: (_, __, ___, ____, _____, ______) => false,
    unauthenticated: (_, __) => false,
    mfaRequired: (_, __, ___, ____) => false,
    error: (_, __, ___, ____) => true,
    sessionExpired: (_, __, ___) => true,
    accountLocked: (_, __, ___) => true,
  );

  /// Get error message if any
  String? get errorMessage => whenOrNull(
    error: (error, _, __, ___) => error.message,
    sessionExpired: (_, reason, __) => reason ?? 'Session expired',
    accountLocked: (reason, _, __) => reason,
  );
}
