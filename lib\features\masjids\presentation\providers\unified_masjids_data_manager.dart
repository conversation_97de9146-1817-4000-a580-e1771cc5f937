import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/errors/app_error.dart';
import '../../../../core/errors/error_details.dart';
import '../../../../core/interfaces/unified_cache_service_interface.dart';
import '../../../../core/providers/supabase_connection_provider.dart';
import '../../../../core/providers/unified_service_providers.dart';
import '../../../../core/services/resilience_service.dart';
import '../../../../core/utils/result.dart';
import '../../data/models/masjid_model.dart';

import 'masjids_providers_simple.dart';

part 'unified_masjids_data_manager.g.dart';

/// Context7 MCP: Unified Masjids Data Manager
///
/// **Single Source of Truth for All Masjid Data Operations**
///
/// This provider consolidates all masjid data management following Context7 MCP best practices:
/// - Uses @riverpod AsyncNotifier pattern for reactive state management
/// - Implements Result types for type-safe error handling
/// - Integrates resilience patterns with circuit breaker and retry mechanisms
/// - Provides comprehensive caching with TTL and background refresh
/// - Maintains backward compatibility with existing provider interfaces
/// - Follows clean architecture principles with repository pattern
///
/// **Replaces the following duplicate providers:**
/// - MasjidsNotifier (supabase_masjids_provider.dart)
/// - SimpleMasjids (masjids_providers_simple.dart)
/// - MasjidsCompositeProvider (masjids_composite_provider.dart)
/// - masjidsDataProvider (masjids_data_provider.dart)
///
/// **Key Features:**
/// - **Performance**: Advanced caching with 24-hour TTL and background refresh
/// - **Resilience**: Circuit breaker protection for all network operations
/// - **Error Handling**: Type-safe Result types with graceful degradation
/// - **Memory Management**: Automatic disposal and cleanup
/// - **Observability**: Comprehensive logging and performance metrics
///
/// **Usage:**
/// ```dart
/// // Watch all masjids
/// final masjidsAsync = ref.watch(unifiedMasjidsDataManagerProvider);
///
/// // Handle states
/// masjidsAsync.when(
///   data: (masjids) => MasjidsList(masjids: masjids),
///   loading: () => const CircularProgressIndicator(),
///   error: (error, stack) => ErrorWidget(error: error),
/// );
///
/// // Refresh data
/// ref.read(unifiedMasjidsDataManagerProvider.notifier).refresh();
///
/// // Load by governorate
/// ref.read(unifiedMasjidsDataManagerProvider.notifier).loadByGovernorate('gov_id');
/// ```
@riverpod
class UnifiedMasjidsDataManager extends _$UnifiedMasjidsDataManager {
  /// Cache key for storing masjids data
  static const String _cacheKey = 'unified_masjids_data';

  /// Public getter for cache key (for debugging and testing)
  static String get cacheKey => _cacheKey;

  /// Cache duration for masjids data (24 hours)
  static const Duration _cacheDuration = Duration(hours: 24);

  /// Operation key for resilience service
  static const String _operationKey = 'unified_masjids_data_fetch';

  /// Background refresh operation key
  static const String _backgroundRefreshKey = 'unified_masjids_background_refresh';

  /// Background sync operation key
  static const String _backgroundSyncKey = 'unified_masjids_background_sync';

  /// Context7 MCP: Advanced caching configuration
  /// Cache tags for tag-based invalidation following Bentocache patterns
  static const List<String> _cacheTags = ['masjids', 'location_data', 'religious_data', 'unified_data'];

  /// Cache namespace for logical grouping
  static const String _cacheNamespace = 'masjids_domain';

  /// Adaptive TTL configuration based on data freshness
  static const Map<String, Duration> _adaptiveTtlConfig = {
    'fresh_data': Duration(hours: 24), // Recently updated data
    'stale_data': Duration(hours: 6), // Older data, refresh more frequently
    'fallback_data': Duration(days: 7), // Emergency fallback data
    'preload_data': Duration(hours: 48), // Preloaded data for performance
  };

  /// Cache warming configuration
  static const Map<String, dynamic> _cacheWarmingConfig = {
    'enabled': true,
    'preload_governorates': ['manama', 'muharraq', 'riffa'],
    'background_refresh_interval': Duration(hours: 12),
    'warm_on_startup': true,
  };

  /// Context7 MCP: Background sync configuration following PowerSync patterns
  static const Map<String, dynamic> _backgroundSyncConfig = {
    'enabled': true,
    'sync_interval': Duration(minutes: 30), // Sync every 30 minutes when app is active
    'background_sync_interval': Duration(hours: 2), // Background sync every 2 hours
    'max_sync_retries': 3,
    'sync_timeout': Duration(minutes: 5),
    'require_wifi_for_background': false, // Allow cellular for critical updates
    'require_charging_for_background': false, // Don't require charging
    'sync_on_app_resume': true, // Sync when app comes to foreground
    'sync_on_network_restore': true, // Sync when network is restored
    'conflict_resolution_strategy': 'server_wins', // Server data takes precedence
    'incremental_sync_enabled': true, // Enable delta sync for efficiency
    'batch_size': 50, // Process updates in batches
    'priority_sync_governorates': ['manama', 'muharraq'], // High-priority locations
  };

  /// Context7 MCP: Error classification for comprehensive error handling
  ///
  /// Following Context7 MCP best practices for error categorization:
  /// - Critical: System-breaking errors requiring immediate attention
  /// - Recoverable: Errors that can be handled with fallback mechanisms
  /// - Transient: Temporary errors that may resolve with retry
  /// - User: User-facing errors requiring user action
  static const Map<String, List<Type>> _errorClassification = {
    'critical': [SecurityError, AuthError, PermissionError],
    'recoverable': [NetworkError, TimeoutError, ServerError, CacheError],
    'transient': [RateLimitError, TimeoutError],
    'user': [ValidationError, NotFoundError, ParsingError],
  };

  /// Context7 MCP: Error recovery strategies configuration
  ///
  /// Defines recovery strategies for different error types following
  /// Context7 MCP best practices for graceful degradation
  static const Map<String, Map<String, dynamic>> _errorRecoveryStrategies = {
    'network_error': {
      'strategy': 'cache_fallback',
      'max_retries': 3,
      'retry_delay': Duration(seconds: 2),
      'fallback_ttl': Duration(hours: 24),
    },
    'cache_error': {
      'strategy': 'direct_fetch',
      'max_retries': 1,
      'retry_delay': Duration(milliseconds: 500),
      'fallback_ttl': Duration(minutes: 5),
    },
    'timeout_error': {
      'strategy': 'exponential_backoff',
      'max_retries': 5,
      'retry_delay': Duration(seconds: 1),
      'fallback_ttl': Duration(hours: 6),
    },
    'server_error': {
      'strategy': 'stale_data_fallback',
      'max_retries': 2,
      'retry_delay': Duration(seconds: 5),
      'fallback_ttl': Duration(days: 1),
    },
  };

  /// Context7 MCP: User-friendly error message mapping
  ///
  /// Maps technical error types to user-friendly localized message keys
  /// following Context7 MCP best practices for error communication
  static const Map<Type, String> _errorMessageMapping = {
    NetworkError: 'networkErrorMessage',
    TimeoutError: 'timeoutErrorMessage',
    ServerError: 'serverErrorMessage',
    CacheError: 'cacheErrorMessage',
    AuthError: 'authErrorMessage',
    ValidationError: 'validationErrorMessage',
    NotFoundError: 'notFoundErrorMessage',
    PermissionError: 'permissionErrorMessage',
    RateLimitError: 'rateLimitErrorMessage',
    SecurityError: 'securityErrorMessage',
    ParsingError: 'parsingErrorMessage',
    LocationError: 'locationErrorMessage',
    NotImplementedError: 'notImplementedErrorMessage',
    UnknownError: 'unknownErrorMessage',
  };

  /// Context7 MCP: Background sync state management
  Timer? _backgroundSyncTimer;
  Timer? _foregroundSyncTimer;
  DateTime? _lastSyncTime;
  DateTime? _lastBackgroundSyncTime;
  bool _isSyncInProgress = false;
  bool _isBackgroundSyncEnabled = true;

  /// Context7 MCP: Initialize with single source of truth pattern
  @override
  Future<List<MasjidModel>> build() async {
    debugPrint('UnifiedMasjidsDataManager: Initializing with Context7 MCP patterns');

    // TODO: Listen for cache invalidation events when provider is implemented
    // ref.listen(cacheInvalidationProvider, (previous, next) {
    //   if (next.shouldInvalidateMasjids) {
    //     debugPrint('UnifiedMasjidsDataManager: Cache invalidation triggered');
    //     ref.invalidateSelf();
    //   }
    // });

    // Listen for connection status changes for background refresh
    ref.listen(supabaseConnectionStatusNotifierProvider, (previous, next) {
      if (previous != next && next == SupabaseConnectionStatus.connected) {
        debugPrint('UnifiedMasjidsDataManager: Connection restored, scheduling background refresh');
        _scheduleBackgroundRefresh();

        // Context7 MCP: Also trigger background sync when connection is restored
        if (_backgroundSyncConfig['sync_on_network_restore'] as bool) {
          _scheduleBackgroundSync();
        }
      }
    });

    // Context7 MCP: Initialize background sync mechanisms
    _initializeBackgroundSync();

    return _loadMasjidsWithResilience();
  }

  /// Context7 MCP: Load masjids with comprehensive resilience patterns
  ///
  /// Enhanced with Context7 MCP resilience patterns following Cockatiel/Polly best practices:
  /// - Circuit breaker with consecutive and sampling failure detection
  /// - Exponential backoff with jitter for retry logic
  /// - Bulkhead isolation for resource protection
  /// - Graceful degradation with fallback mechanisms
  /// - State-aware retry delays based on circuit breaker status
  /// - Comprehensive error handling with Result types
  Future<List<MasjidModel>> _loadMasjidsWithResilience() async {
    debugPrint('UnifiedMasjidsDataManager: Loading masjids with enhanced resilience patterns');

    // Context7 MCP: Try cache first with circuit breaker awareness
    final cacheResult = await _executeWithEnhancedResilience<List<MasjidModel>>(
      operationKey: '${_operationKey}_cache_read',
      operation: () async {
        final cachedData = await _getCachedData();
        if (cachedData != null && cachedData.isNotEmpty) {
          return cachedData;
        }
        throw const AppError.cache('Cache miss or empty data');
      },
      fallback: () async => <MasjidModel>[], // Empty list as fallback for cache miss
      config: _createCacheResilienceConfig(),
    );

    // Context7 MCP: If cache hit, schedule background refresh and return
    if (cacheResult.isSuccess && cacheResult.dataOrNull!.isNotEmpty) {
      debugPrint('UnifiedMasjidsDataManager: Loaded ${cacheResult.dataOrNull!.length} masjids from cache');

      // Schedule background refresh with circuit breaker awareness
      _scheduleBackgroundRefresh();

      return cacheResult.dataOrNull!;
    }

    // Context7 MCP: Load from repository with enhanced resilience patterns
    final repositoryResult = await _executeWithEnhancedResilience<List<MasjidModel>>(
      operationKey: _operationKey,
      operation: () async {
        final repository = ref.read(masjidsRepositoryProvider);
        final repositoryResult = await repository.getAllMasjids();

        if (repositoryResult.isFailure) {
          throw repositoryResult.errorOrNull ?? const AppError.network('Failed to load masjids');
        }

        // Convert domain entities to models
        final entities = repositoryResult.dataOrNull!;
        return entities.map((entity) => entity.toModel()).toList();
      },
      fallback: () async {
        // Context7 MCP: Graceful degradation - try to return stale cache data
        final staleData = await _getStaleData();
        if (staleData != null && staleData.isNotEmpty) {
          debugPrint('UnifiedMasjidsDataManager: Returning stale cache data as fallback');
          return staleData;
        }

        // Ultimate fallback - empty list
        debugPrint('UnifiedMasjidsDataManager: No fallback data available, returning empty list');
        return <MasjidModel>[];
      },
      config: _createRepositoryResilienceConfig(),
    );

    if (repositoryResult.isSuccess) {
      // Cache the results with both fresh and stale keys
      await _cacheResults(repositoryResult.dataOrNull!);

      debugPrint('UnifiedMasjidsDataManager: Loaded ${repositoryResult.dataOrNull!.length} masjids from repository');
      return repositoryResult.dataOrNull!;
    }

    // Context7 MCP: If all else fails, return fallback data
    final fallbackData = repositoryResult.dataOrNull ?? <MasjidModel>[];
    debugPrint('UnifiedMasjidsDataManager: Returning fallback data with ${fallbackData.length} masjids');
    return fallbackData;
  }

  /// Context7 MCP: Execute operation with enhanced resilience patterns
  ///
  /// Implements Context7 MCP best practices following Cockatiel/Polly patterns:
  /// - Circuit breaker with state-aware retry delays
  /// - Exponential backoff with jitter
  /// - Bulkhead isolation
  /// - Comprehensive error handling with Result types
  /// - Graceful degradation with fallback mechanisms
  Future<Result<T>> _executeWithEnhancedResilience<T>({
    required String operationKey,
    required Future<T> Function() operation,
    Future<T> Function()? fallback,
    required ResilienceConfig config,
  }) async {
    try {
      // Context7 MCP: Execute with ResilienceService enhanced patterns
      final result = await ResilienceService.executeWithResilience<T>(
        operationKey: operationKey,
        operation: operation,
        fallback: fallback,
        config: config,
      );

      return Result.success(result);
    } on Exception catch (error, stackTrace) {
      debugPrint('UnifiedMasjidsDataManager: Enhanced resilience operation failed: $error');

      // Context7 MCP: Process error with comprehensive error handling
      final processedError = await _processErrorWithRecovery(
        error,
        operationKey: operationKey,
        stackTrace: stackTrace,
        fallback: fallback,
      );

      return Result.failure(processedError);
    }
  }

  /// Context7 MCP: Process error with comprehensive recovery strategies
  ///
  /// Implements Context7 MCP best practices for error processing:
  /// - Error classification and categorization
  /// - Recovery strategy selection based on error type
  /// - User-friendly error message generation
  /// - Error logging and monitoring integration
  /// - Graceful degradation with intelligent fallbacks
  Future<AppError> _processErrorWithRecovery(
    dynamic error, {
    required String operationKey,
    StackTrace? stackTrace,
    Future<dynamic> Function()? fallback,
  }) async {
    // Context7 MCP: Convert to standardized AppError
    final appError = _convertToAppError(error, stackTrace);

    // Context7 MCP: Classify error for appropriate handling
    final errorCategory = _classifyError(appError);

    // Context7 MCP: Log error with comprehensive context
    await _logErrorWithContext(appError, operationKey, errorCategory);

    // Context7 MCP: Attempt recovery based on error type and category
    final recoveredError = await _attemptErrorRecovery(appError, errorCategory, operationKey, fallback);

    // Context7 MCP: Enhance error with user-friendly messaging
    return _enhanceErrorWithUserFriendlyMessage(recoveredError);
  }

  /// Context7 MCP: Convert any error to standardized AppError
  ///
  /// Following Context7 MCP best practices for error standardization
  AppError _convertToAppError(dynamic error, StackTrace? stackTrace) {
    if (error is AppError) {
      return error;
    }

    // Context7 MCP: Handle common error types with proper classification
    if (error is Exception) {
      final errorString = error.toString().toLowerCase();

      if (errorString.contains('network') || errorString.contains('connection')) {
        return AppError.network(
          'Network operation failed',
          details: GenericErrorDetails({'originalError': error.toString(), 'errorType': error.runtimeType.toString()}),
          stackTrace: stackTrace,
        );
      }

      if (errorString.contains('timeout')) {
        return AppError.timeout(
          'Operation timed out',
          operation: 'masjids_data_fetch',
          details: GenericErrorDetails({'originalError': error.toString(), 'errorType': error.runtimeType.toString()}),
          stackTrace: stackTrace,
        );
      }

      if (errorString.contains('cache')) {
        return AppError.cache(
          'Cache operation failed',
          operation: 'masjids_cache_access',
          details: GenericErrorDetails({'originalError': error.toString(), 'errorType': error.runtimeType.toString()}),
          stackTrace: stackTrace,
        );
      }
    }

    // Context7 MCP: Default to unknown error with full context
    return AppError.unknown(
      'Unexpected error occurred',
      originalError: error.toString(),
      details: GenericErrorDetails({
        'originalError': error.toString(),
        'errorType': error.runtimeType.toString(),
        'operationContext': 'unified_masjids_data_manager',
      }),
      stackTrace: stackTrace,
    );
  }

  /// Context7 MCP: Classify error for appropriate handling strategy
  ///
  /// Returns error category for recovery strategy selection
  String _classifyError(AppError error) {
    final errorType = error.runtimeType;

    // Context7 MCP: Check each classification category
    for (final entry in _errorClassification.entries) {
      if (entry.value.contains(errorType)) {
        return entry.key;
      }
    }

    // Context7 MCP: Default to recoverable for unknown error types
    return 'recoverable';
  }

  /// Context7 MCP: Log error with comprehensive context
  ///
  /// Implements Context7 MCP best practices for error logging:
  /// - Structured logging with error categorization
  /// - Operation context and metadata preservation
  /// - Performance impact monitoring
  /// - Security-safe error information
  Future<void> _logErrorWithContext(AppError error, String operationKey, String errorCategory) async {
    try {
      // Context7 MCP: Create comprehensive error context
      final errorContext = {
        'operation_key': operationKey,
        'error_category': errorCategory,
        'error_type': error.runtimeType.toString(),
        'timestamp': DateTime.now().toIso8601String(),
        'provider': 'unified_masjids_data_manager',
        'severity': _determineSeverity(errorCategory),
      };

      // Context7 MCP: Log with appropriate level based on category
      switch (errorCategory) {
        case 'critical':
          debugPrint('🚨 CRITICAL ERROR [UnifiedMasjidsDataManager]: $error');
          debugPrint('Context: $errorContext');
          break;
        case 'recoverable':
          debugPrint('⚠️ RECOVERABLE ERROR [UnifiedMasjidsDataManager]: $error');
          debugPrint('Context: $errorContext');
          break;
        case 'transient':
          debugPrint('🔄 TRANSIENT ERROR [UnifiedMasjidsDataManager]: $error');
          break;
        case 'user':
          debugPrint('👤 USER ERROR [UnifiedMasjidsDataManager]: $error');
          break;
        default:
          debugPrint('❓ UNKNOWN ERROR [UnifiedMasjidsDataManager]: $error');
          debugPrint('Context: $errorContext');
      }

      // Context7 MCP: Additional logging for monitoring (placeholder for analytics)
      // TODO: Integrate with analytics service when available
      // await AnalyticsService.logError(error, errorContext);
    } on Exception catch (loggingError) {
      // Context7 MCP: Ensure logging errors don't break the application
      debugPrint('UnifiedMasjidsDataManager: Failed to log error: $loggingError');
    }
  }

  /// Context7 MCP: Determine error severity based on category
  String _determineSeverity(String errorCategory) {
    switch (errorCategory) {
      case 'critical':
        return 'high';
      case 'recoverable':
        return 'medium';
      case 'transient':
        return 'low';
      case 'user':
        return 'info';
      default:
        return 'medium';
    }
  }

  /// Context7 MCP: Attempt error recovery based on type and category
  ///
  /// Implements Context7 MCP best practices for error recovery:
  /// - Strategy selection based on error classification
  /// - Intelligent fallback mechanisms
  /// - Recovery attempt tracking and limiting
  /// - Graceful degradation patterns
  Future<AppError> _attemptErrorRecovery(
    AppError originalError,
    String errorCategory,
    String operationKey,
    Future<dynamic> Function()? fallback,
  ) async {
    try {
      // Context7 MCP: Skip recovery for critical errors
      if (errorCategory == 'critical') {
        debugPrint('UnifiedMasjidsDataManager: Skipping recovery for critical error');
        return originalError;
      }

      // Context7 MCP: Get recovery strategy for error type
      final errorTypeKey = _getErrorTypeKey(originalError);
      final recoveryConfig = _errorRecoveryStrategies[errorTypeKey];

      if (recoveryConfig == null) {
        debugPrint('UnifiedMasjidsDataManager: No recovery strategy for error type: $errorTypeKey');
        return originalError;
      }

      final strategy = recoveryConfig['strategy'] as String;
      debugPrint('UnifiedMasjidsDataManager: Attempting recovery with strategy: $strategy');

      // Context7 MCP: Execute recovery strategy
      switch (strategy) {
        case 'cache_fallback':
          return await _recoverWithCacheFallback(originalError, operationKey);
        case 'direct_fetch':
          return await _recoverWithDirectFetch(originalError, fallback);
        case 'stale_data_fallback':
          return await _recoverWithStaleData(originalError, operationKey);
        case 'exponential_backoff':
          // For exponential backoff, we return the original error but with recovery context
          return _addRecoveryContext(originalError, 'exponential_backoff_scheduled');
        default:
          debugPrint('UnifiedMasjidsDataManager: Unknown recovery strategy: $strategy');
          return originalError;
      }
    } on Exception catch (recoveryError) {
      debugPrint('UnifiedMasjidsDataManager: Recovery attempt failed: $recoveryError');
      // Context7 MCP: Return original error if recovery fails
      return _addRecoveryContext(originalError, 'recovery_failed');
    }
  }

  /// Context7 MCP: Get error type key for recovery strategy lookup
  String _getErrorTypeKey(AppError error) {
    return error.when(
      network: (_, __, ___, ____) => 'network_error',
      timeout: (_, __, ___, ____, _____) => 'timeout_error',
      server: (_, __, ___, ____) => 'server_error',
      cache: (_, __, ___, ____) => 'cache_error',
      auth: (_, __, ___, ____) => 'network_error', // Treat as network for recovery
      validation: (_, __, ___, ____) => 'user_error',
      permission: (_, __, ___, ____) => 'user_error',
      notFound: (_, __, ___, ____, _____) => 'user_error',
      location: (_, __, ___, ____) => 'network_error',
      security: (_, __, ___, ____) => 'network_error',
      rateLimit: (_, __, ___, ____, _____) => 'timeout_error',
      parsing: (_, __, ___, ____) => 'user_error',
      notImplemented: (_, __, ___, ____) => 'user_error',
      unknown: (_, __, ___, ____) => 'network_error', // Default to network recovery
    );
  }

  /// Context7 MCP: Recover with cache fallback strategy
  Future<AppError> _recoverWithCacheFallback(AppError originalError, String operationKey) async {
    try {
      debugPrint('UnifiedMasjidsDataManager: Attempting cache fallback recovery');

      // Try to get any available cached data
      final cachedData = await _getCachedData();
      if (cachedData != null && cachedData.isNotEmpty) {
        debugPrint('UnifiedMasjidsDataManager: Cache fallback successful, found ${cachedData.length} items');
        // Return success context - the actual data will be handled by the calling method
        return _addRecoveryContext(originalError, 'cache_fallback_successful');
      }

      // Try stale data as secondary fallback
      final staleData = await _getStaleData();
      if (staleData != null && staleData.isNotEmpty) {
        debugPrint('UnifiedMasjidsDataManager: Stale data fallback successful, found ${staleData.length} items');
        return _addRecoveryContext(originalError, 'stale_data_fallback_successful');
      }

      debugPrint('UnifiedMasjidsDataManager: Cache fallback failed - no data available');
      return _addRecoveryContext(originalError, 'cache_fallback_failed');
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Cache fallback recovery failed: $error');
      return _addRecoveryContext(originalError, 'cache_fallback_error');
    }
  }

  /// Context7 MCP: Recover with direct fetch strategy
  Future<AppError> _recoverWithDirectFetch(AppError originalError, Future<dynamic> Function()? fallback) async {
    try {
      if (fallback == null) {
        debugPrint('UnifiedMasjidsDataManager: Direct fetch recovery failed - no fallback provided');
        return _addRecoveryContext(originalError, 'direct_fetch_no_fallback');
      }

      debugPrint('UnifiedMasjidsDataManager: Attempting direct fetch recovery');
      await fallback();
      debugPrint('UnifiedMasjidsDataManager: Direct fetch recovery successful');
      return _addRecoveryContext(originalError, 'direct_fetch_successful');
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Direct fetch recovery failed: $error');
      return _addRecoveryContext(originalError, 'direct_fetch_failed');
    }
  }

  /// Context7 MCP: Recover with stale data fallback strategy
  Future<AppError> _recoverWithStaleData(AppError originalError, String operationKey) async {
    try {
      debugPrint('UnifiedMasjidsDataManager: Attempting stale data recovery');

      final staleData = await _getStaleData();
      if (staleData != null && staleData.isNotEmpty) {
        debugPrint('UnifiedMasjidsDataManager: Stale data recovery successful, found ${staleData.length} items');
        return _addRecoveryContext(originalError, 'stale_data_successful');
      }

      debugPrint('UnifiedMasjidsDataManager: Stale data recovery failed - no stale data available');
      return _addRecoveryContext(originalError, 'stale_data_unavailable');
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Stale data recovery failed: $error');
      return _addRecoveryContext(originalError, 'stale_data_error');
    }
  }

  /// Context7 MCP: Add recovery context to error
  AppError _addRecoveryContext(AppError originalError, String recoveryStatus) {
    final recoveryMetadata = {
      'recovery_attempted': true,
      'recovery_status': recoveryStatus,
      'recovery_timestamp': DateTime.now().toIso8601String(),
    };

    return originalError.when(
      network: (message, code, details, stackTrace) => AppError.network(
        message,
        code: code,
        details: GenericErrorDetails({...?details?.toMap(), ...recoveryMetadata}),
        stackTrace: stackTrace,
      ),
      timeout: (message, duration, operation, details, stackTrace) => AppError.timeout(
        message,
        duration: duration,
        operation: operation,
        details: GenericErrorDetails({...?details?.toMap(), ...recoveryMetadata}),
        stackTrace: stackTrace,
      ),
      server: (message, code, details, stackTrace) => AppError.server(
        message,
        code: code,
        details: GenericErrorDetails({...?details?.toMap(), ...recoveryMetadata}),
        stackTrace: stackTrace,
      ),
      cache: (message, operation, details, stackTrace) => AppError.cache(
        message,
        operation: operation,
        details: GenericErrorDetails({...?details?.toMap(), ...recoveryMetadata}),
        stackTrace: stackTrace,
      ),
      auth: (message, code, details, stackTrace) => AppError.auth(
        message,
        code: code,
        details: GenericErrorDetails({...?details?.toMap(), ...recoveryMetadata}),
        stackTrace: stackTrace,
      ),
      validation: (message, field, details, stackTrace) => AppError.validation(
        message,
        field: field,
        details: GenericErrorDetails({...?details?.toMap(), ...recoveryMetadata}),
        stackTrace: stackTrace,
      ),
      permission: (message, permissionType, details, stackTrace) => AppError.permission(
        message,
        permissionType: permissionType,
        details: GenericErrorDetails({...?details?.toMap(), ...recoveryMetadata}),
        stackTrace: stackTrace,
      ),
      notFound: (message, resourceType, resourceId, details, stackTrace) => AppError.notFound(
        message,
        resourceType: resourceType,
        resourceId: resourceId,
        details: GenericErrorDetails({...?details?.toMap(), ...recoveryMetadata}),
        stackTrace: stackTrace,
      ),
      location: (message, code, details, stackTrace) => AppError.location(
        message,
        code: code,
        details: GenericErrorDetails({...?details?.toMap(), ...recoveryMetadata}),
        stackTrace: stackTrace,
      ),
      security: (message, code, details, stackTrace) => AppError.security(
        message,
        code: code,
        details: GenericErrorDetails({...?details?.toMap(), ...recoveryMetadata}),
        stackTrace: stackTrace,
      ),
      rateLimit: (message, retryAfter, remainingAttempts, details, stackTrace) => AppError.rateLimit(
        message,
        retryAfter: retryAfter,
        remainingAttempts: remainingAttempts,
        details: GenericErrorDetails({...?details?.toMap(), ...recoveryMetadata}),
        stackTrace: stackTrace,
      ),
      parsing: (message, field, details, stackTrace) => AppError.parsing(
        message,
        field: field,
        details: GenericErrorDetails({...?details?.toMap(), ...recoveryMetadata}),
        stackTrace: stackTrace,
      ),
      notImplemented: (message, feature, details, stackTrace) => AppError.notImplemented(
        message,
        feature: feature,
        details: GenericErrorDetails({...?details?.toMap(), ...recoveryMetadata}),
        stackTrace: stackTrace,
      ),
      unknown: (message, originalError, details, stackTrace) => AppError.unknown(
        message,
        originalError: originalError,
        details: GenericErrorDetails({...?details?.toMap(), ...recoveryMetadata}),
        stackTrace: stackTrace,
      ),
    );
  }

  /// Context7 MCP: Enhance error with user-friendly messaging
  ///
  /// Implements Context7 MCP best practices for user-facing error messages:
  /// - Maps technical errors to user-friendly messages
  /// - Provides localized error descriptions
  /// - Includes actionable guidance for users
  /// - Maintains technical details for debugging
  AppError _enhanceErrorWithUserFriendlyMessage(AppError error) {
    try {
      // Context7 MCP: Get user-friendly message key for error type
      final messageKey = _errorMessageMapping[error.runtimeType] ?? 'unknownErrorMessage';

      // Context7 MCP: Create user-friendly metadata
      final userFriendlyMetadata = {
        'user_friendly_message_key': messageKey,
        'error_category': _classifyError(error),
        'user_actionable': _isUserActionable(error),
        'retry_recommended': _isRetryRecommended(error),
        'enhanced_at': DateTime.now().toIso8601String(),
      };

      // Context7 MCP: Enhance error with user-friendly context
      return error.when(
        network: (message, code, details, stackTrace) => AppError.network(
          'Network connection issue - please check your internet connection',
          code: code,
          details: GenericErrorDetails({...?details?.toMap(), ...userFriendlyMetadata}),
          stackTrace: stackTrace,
        ),
        timeout: (message, duration, operation, details, stackTrace) => AppError.timeout(
          'Request timed out - please try again',
          duration: duration,
          operation: operation,
          details: GenericErrorDetails({...?details?.toMap(), ...userFriendlyMetadata}),
          stackTrace: stackTrace,
        ),
        server: (message, code, details, stackTrace) => AppError.server(
          'Server is temporarily unavailable - please try again later',
          code: code,
          details: GenericErrorDetails({...?details?.toMap(), ...userFriendlyMetadata}),
          stackTrace: stackTrace,
        ),
        cache: (message, operation, details, stackTrace) => AppError.cache(
          'Data loading issue - refreshing may help',
          operation: operation,
          details: GenericErrorDetails({...?details?.toMap(), ...userFriendlyMetadata}),
          stackTrace: stackTrace,
        ),
        auth: (message, code, details, stackTrace) => AppError.auth(
          'Authentication required - please sign in',
          code: code,
          details: GenericErrorDetails({...?details?.toMap(), ...userFriendlyMetadata}),
          stackTrace: stackTrace,
        ),
        validation: (message, field, details, stackTrace) => AppError.validation(
          'Please check your input and try again',
          field: field,
          details: GenericErrorDetails({...?details?.toMap(), ...userFriendlyMetadata}),
          stackTrace: stackTrace,
        ),
        permission: (message, permissionType, details, stackTrace) => AppError.permission(
          'Permission required - please check your access rights',
          permissionType: permissionType,
          details: GenericErrorDetails({...?details?.toMap(), ...userFriendlyMetadata}),
          stackTrace: stackTrace,
        ),
        notFound: (message, resourceType, resourceId, details, stackTrace) => AppError.notFound(
          'Requested information not found',
          resourceType: resourceType,
          resourceId: resourceId,
          details: GenericErrorDetails({...?details?.toMap(), ...userFriendlyMetadata}),
          stackTrace: stackTrace,
        ),
        location: (message, code, details, stackTrace) => AppError.location(
          'Location access issue - please check permissions',
          code: code,
          details: GenericErrorDetails({...?details?.toMap(), ...userFriendlyMetadata}),
          stackTrace: stackTrace,
        ),
        security: (message, code, details, stackTrace) => AppError.security(
          'Security check failed - please try again',
          code: code,
          details: GenericErrorDetails({...?details?.toMap(), ...userFriendlyMetadata}),
          stackTrace: stackTrace,
        ),
        rateLimit: (message, retryAfter, remainingAttempts, details, stackTrace) => AppError.rateLimit(
          'Too many requests - please wait a moment and try again',
          retryAfter: retryAfter,
          remainingAttempts: remainingAttempts,
          details: GenericErrorDetails({...?details?.toMap(), ...userFriendlyMetadata}),
          stackTrace: stackTrace,
        ),
        parsing: (message, field, details, stackTrace) => AppError.parsing(
          'Data format issue - please refresh and try again',
          field: field,
          details: GenericErrorDetails({...?details?.toMap(), ...userFriendlyMetadata}),
          stackTrace: stackTrace,
        ),
        notImplemented: (message, feature, details, stackTrace) => AppError.notImplemented(
          'Feature not available - coming soon',
          feature: feature,
          details: GenericErrorDetails({...?details?.toMap(), ...userFriendlyMetadata}),
          stackTrace: stackTrace,
        ),
        unknown: (message, originalError, details, stackTrace) => AppError.unknown(
          'Something went wrong - please try again',
          originalError: originalError,
          details: GenericErrorDetails({...?details?.toMap(), ...userFriendlyMetadata}),
          stackTrace: stackTrace,
        ),
      );
    } on Exception catch (enhancementError) {
      debugPrint('UnifiedMasjidsDataManager: Failed to enhance error message: $enhancementError');
      // Context7 MCP: Return original error if enhancement fails
      return error;
    }
  }

  /// Context7 MCP: Check if error is user-actionable
  bool _isUserActionable(AppError error) {
    final errorType = error.runtimeType;
    return [ValidationError, AuthError, PermissionError, NotFoundError].contains(errorType);
  }

  /// Context7 MCP: Check if retry is recommended for error type
  bool _isRetryRecommended(AppError error) {
    final errorType = error.runtimeType;
    return [NetworkError, TimeoutError, ServerError, RateLimitError].contains(errorType);
  }

  /// Context7 MCP: Create cache-specific resilience configuration
  ///
  /// Optimized for cache operations with:
  /// - Lower failure threshold (cache should be fast)
  /// - Shorter timeouts
  /// - Minimal retries (cache miss is acceptable)
  ResilienceConfig _createCacheResilienceConfig() {
    return ResilienceConfig(
      failureThreshold: 2, // Lower threshold for cache operations
      timeout: const Duration(seconds: 5), // Shorter timeout for cache
      circuitBreakerTimeout: const Duration(seconds: 10), // Quick recovery
      recoveryTime: const Duration(seconds: 15), // Fast recovery for cache
      maxRetries: 1, // Minimal retries for cache
      initialDelay: const Duration(milliseconds: 100), // Quick initial retry
      backoffMultiplier: 1.5, // Gentle backoff for cache
      maxDelay: const Duration(seconds: 2), // Short max delay
      bulkheadCapacity: 5, // Lower capacity for cache operations
      bulkheadQueueSize: 10, // Smaller queue for cache
      shouldRetry: (error) {
        // Context7 MCP: Only retry on specific cache errors
        return error is AppError && (error is CacheError || error is NetworkError);
      },
    );
  }

  /// Context7 MCP: Create repository-specific resilience configuration
  ///
  /// Optimized for repository operations with:
  /// - Higher failure threshold (network operations can be flaky)
  /// - Longer timeouts
  /// - More aggressive retries
  ResilienceConfig _createRepositoryResilienceConfig() {
    return ResilienceConfig(
      failureThreshold: 5, // Higher threshold for network operations
      timeout: const Duration(seconds: 30), // Standard timeout
      circuitBreakerTimeout: const Duration(seconds: 60), // Longer circuit breaker timeout
      recoveryTime: const Duration(minutes: 2), // Longer recovery for network issues
      maxRetries: 3, // More retries for critical operations
      initialDelay: const Duration(seconds: 1), // Standard initial delay
      backoffMultiplier: 2.0, // Exponential backoff
      maxDelay: const Duration(seconds: 30), // Longer max delay
      bulkheadCapacity: 10, // Standard capacity
      bulkheadQueueSize: 20, // Standard queue size
      shouldRetry: (error) {
        // Context7 MCP: Retry on network and temporary errors
        return error is AppError && (error is NetworkError || error is TimeoutError || error is ServerError);
      },
    );
  }

  /// Context7 MCP: Get stale data for graceful degradation
  ///
  /// Attempts to retrieve stale cached data as a fallback mechanism
  Future<List<MasjidModel>?> _getStaleData() async {
    try {
      final cacheService = await ref.read(unifiedCacheServiceProvider.future);

      // Try to get stale data from a separate cache key
      final staleResult = await cacheService.get<List<dynamic>>('${_cacheKey}_stale');

      if (staleResult.isSuccess && staleResult.dataOrNull != null) {
        final jsonList = staleResult.dataOrNull!;
        return jsonList.map((item) => MasjidModel.fromJson(item)).toList();
      }

      // If no stale data, try the regular cache key (might be expired but still usable)
      final regularResult = await cacheService.get<List<dynamic>>(_cacheKey);

      if (regularResult.isSuccess && regularResult.dataOrNull != null) {
        final jsonList = regularResult.dataOrNull!;
        return jsonList.map((item) => MasjidModel.fromJson(item)).toList();
      }

      return null;
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Failed to get stale data: $error');
      return null;
    }
  }

  /// Context7 MCP: Cache results with advanced TTL and tag-based invalidation
  ///
  /// Implements Bentocache-inspired patterns:
  /// - Adaptive TTL based on data freshness
  /// - Tag-based invalidation for logical grouping
  /// - Multi-tier caching with namespace support
  /// - Cache warming integration
  Future<void> _cacheResults(List<MasjidModel> masjids) async {
    try {
      final cacheService = await ref.read(unifiedCacheServiceProvider.future);

      // Context7 MCP: Determine adaptive TTL based on data characteristics
      final adaptiveTtl = _determineAdaptiveTtl(masjids);

      // Context7 MCP: Create namespaced cache key following Bentocache patterns
      const namespacedKey = '$_cacheNamespace:$_cacheKey';

      // Context7 MCP: Cache with tags for invalidation support
      final result = await cacheService.set(
        namespacedKey,
        masjids.map((m) => m.toJson()).toList(),
        expiration: adaptiveTtl,
        tags: _cacheTags,
        strategy: CacheStrategy.intelligent,
        priority: CachePriority.high,
      );

      if (result.isFailure) {
        debugPrint('UnifiedMasjidsDataManager: Advanced cache set failed: ${result.errorOrNull}');
        // Fallback to basic caching
        await _fallbackBasicCaching(masjids);
        return;
      }

      debugPrint(
        'UnifiedMasjidsDataManager: Advanced cached ${masjids.length} masjids with TTL: $adaptiveTtl, tags: $_cacheTags',
      );

      // Context7 MCP: Trigger cache warming for related data
      await _triggerCacheWarming(masjids);
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Failed to cache results with advanced features: $error');
      // Fallback to basic caching
      await _fallbackBasicCaching(masjids);
    }
  }

  /// Context7 MCP: Get cached data with error handling
  Future<List<MasjidModel>?> _getCachedData() async {
    try {
      final cacheService = await ref.read(unifiedCacheServiceProvider.future);
      final result = await cacheService.get<List<dynamic>>(_cacheKey);

      if (result.isFailure || result.dataOrNull == null) {
        debugPrint('UnifiedMasjidsDataManager: No cached data available');
        return null;
      }

      final cachedData = result.dataOrNull!;
      final masjids = cachedData
          .map((json) => MasjidModel.fromJsonWithProcessing(json as Map<String, dynamic>))
          .toList();
      debugPrint('UnifiedMasjidsDataManager: Retrieved ${masjids.length} masjids from cache');
      return masjids;
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Failed to get cached data: $error');
      return null;
    }
  }

  /// Context7 MCP: Schedule background refresh without blocking UI
  void _scheduleBackgroundRefresh() {
    // Schedule background refresh after a delay to avoid blocking current operation
    Future.delayed(const Duration(seconds: 30), () async {
      try {
        debugPrint('UnifiedMasjidsDataManager: Starting background refresh');
        await _performBackgroundRefresh();
      } on Exception catch (error) {
        debugPrint('UnifiedMasjidsDataManager: Background refresh failed: $error');
        // Background refresh failures are non-critical
      }
    });
  }

  /// Context7 MCP: Perform background refresh with enhanced resilience patterns
  ///
  /// Uses enhanced resilience patterns for background operations:
  /// - Lower failure threshold for background operations
  /// - Graceful degradation without affecting current state
  /// - Optimized retry strategy for background tasks
  /// - Comprehensive error handling with fallback mechanisms
  Future<void> _performBackgroundRefresh() async {
    // Context7 MCP: Use enhanced resilience for background refresh
    final refreshResult = await _executeWithEnhancedResilience<List<MasjidModel>>(
      operationKey: _backgroundRefreshKey,
      operation: () async {
        final repository = ref.read(masjidsRepositoryProvider);
        final repositoryResult = await repository.getAllMasjids();

        if (repositoryResult.isFailure) {
          throw repositoryResult.errorOrNull ?? const AppError.network('Background refresh failed');
        }

        // Convert domain entities to models
        final entities = repositoryResult.dataOrNull!;
        return entities.map((entity) => entity.toModel()).toList();
      },
      fallback: () async {
        // Context7 MCP: For background refresh, fallback to current cached data
        final cachedData = await _getCachedData();
        return cachedData ?? <MasjidModel>[];
      },
      config: _createBackgroundRefreshResilienceConfig(),
    );

    if (refreshResult.isSuccess && refreshResult.dataOrNull != null) {
      final freshData = refreshResult.dataOrNull!;

      // Update cache with fresh data (including stale backup)
      await _cacheResults(freshData);
      await _cacheStaleData(freshData);

      // Update state if data has changed
      final currentData = state.value;
      if (currentData == null || !_listsEqual(currentData, freshData)) {
        debugPrint('UnifiedMasjidsDataManager: Background refresh detected changes, updating state');
        state = AsyncValue.data(freshData);
      }
    } else {
      debugPrint('UnifiedMasjidsDataManager: Background refresh failed, keeping current state');
    }
  }

  /// Context7 MCP: Create background refresh-specific resilience configuration
  ///
  /// Optimized for background operations with:
  /// - Lower failure threshold (background operations should be less aggressive)
  /// - Shorter timeouts (don't block for too long in background)
  /// - Fewer retries (background operations can fail gracefully)
  ResilienceConfig _createBackgroundRefreshResilienceConfig() {
    return ResilienceConfig(
      failureThreshold: 3, // Lower threshold for background operations
      timeout: const Duration(seconds: 20), // Shorter timeout for background
      circuitBreakerTimeout: const Duration(seconds: 30), // Quick circuit breaker
      recoveryTime: const Duration(minutes: 1), // Fast recovery for background
      maxRetries: 2, // Fewer retries for background operations
      initialDelay: const Duration(milliseconds: 500), // Quick initial retry
      backoffMultiplier: 1.5, // Gentle backoff for background
      maxDelay: const Duration(seconds: 10), // Short max delay
      bulkheadCapacity: 3, // Lower capacity for background operations
      bulkheadQueueSize: 5, // Smaller queue for background
      shouldRetry: (error) {
        // Context7 MCP: Only retry on network and timeout errors for background
        return error is AppError && (error is NetworkError || error is TimeoutError);
      },
    );
  }

  /// Context7 MCP: Initialize background sync mechanisms
  ///
  /// Sets up intelligent background sync following PowerSync patterns:
  /// - Foreground sync timer for active app usage
  /// - Background sync timer for periodic updates
  /// - App lifecycle awareness for optimal sync timing
  /// - Network-aware sync scheduling
  void _initializeBackgroundSync() {
    if (!(_backgroundSyncConfig['enabled'] as bool)) {
      debugPrint('UnifiedMasjidsDataManager: Background sync disabled');
      return;
    }

    debugPrint('UnifiedMasjidsDataManager: Initializing background sync mechanisms');

    // Context7 MCP: Schedule foreground sync timer (more frequent when app is active)
    _scheduleForegroundSync();

    // Context7 MCP: Schedule background sync timer (less frequent, battery-optimized)
    _scheduleBackgroundSync();

    debugPrint('UnifiedMasjidsDataManager: Background sync mechanisms initialized');
  }

  /// Context7 MCP: Schedule foreground sync timer
  ///
  /// Implements intelligent foreground sync following Context7 MCP patterns:
  /// - More frequent sync when app is actively used
  /// - Respects user data preferences and network conditions
  /// - Graceful handling of sync conflicts and errors
  void _scheduleForegroundSync() {
    // Cancel existing timer if any
    _foregroundSyncTimer?.cancel();

    if (!_isBackgroundSyncEnabled) {
      debugPrint('UnifiedMasjidsDataManager: Foreground sync disabled');
      return;
    }

    final syncInterval = _backgroundSyncConfig['sync_interval'] as Duration;

    debugPrint('UnifiedMasjidsDataManager: Scheduling foreground sync every ${syncInterval.inMinutes} minutes');

    _foregroundSyncTimer = Timer.periodic(syncInterval, (timer) async {
      if (_isSyncInProgress) {
        debugPrint('UnifiedMasjidsDataManager: Skipping foreground sync - sync already in progress');
        return;
      }

      try {
        await _performIntelligentSync(isForegroundSync: true);
      } on Exception catch (error) {
        debugPrint('UnifiedMasjidsDataManager: Foreground sync failed: $error');
        // Foreground sync failures are logged but don't stop the timer
      }
    });
  }

  /// Context7 MCP: Schedule background sync timer
  ///
  /// Implements battery-optimized background sync following PowerSync patterns:
  /// - Less frequent sync to preserve battery life
  /// - Network-aware scheduling (prefer WiFi for large updates)
  /// - Intelligent conflict resolution and data prioritization
  void _scheduleBackgroundSync() {
    // Cancel existing timer if any
    _backgroundSyncTimer?.cancel();

    if (!_isBackgroundSyncEnabled) {
      debugPrint('UnifiedMasjidsDataManager: Background sync disabled');
      return;
    }

    final backgroundSyncInterval = _backgroundSyncConfig['background_sync_interval'] as Duration;

    debugPrint('UnifiedMasjidsDataManager: Scheduling background sync every ${backgroundSyncInterval.inHours} hours');

    _backgroundSyncTimer = Timer.periodic(backgroundSyncInterval, (timer) async {
      if (_isSyncInProgress) {
        debugPrint('UnifiedMasjidsDataManager: Skipping background sync - sync already in progress');
        return;
      }

      try {
        await _performIntelligentSync(isForegroundSync: false);
      } on Exception catch (error) {
        debugPrint('UnifiedMasjidsDataManager: Background sync failed: $error');
        // Background sync failures are logged but don't stop the timer
      }
    });
  }

  /// Context7 MCP: Perform intelligent sync with conflict resolution
  ///
  /// Implements comprehensive sync logic following PowerSync patterns:
  /// - Incremental sync for efficiency (delta updates only)
  /// - Intelligent conflict resolution with server-wins strategy
  /// - Priority-based sync for high-importance locations
  /// - Network-aware sync optimization
  /// - Graceful degradation on sync failures
  Future<void> _performIntelligentSync({required bool isForegroundSync}) async {
    if (_isSyncInProgress) {
      debugPrint('UnifiedMasjidsDataManager: Sync already in progress, skipping');
      return;
    }

    final syncType = isForegroundSync ? 'foreground' : 'background';
    debugPrint('UnifiedMasjidsDataManager: Starting intelligent $syncType sync');

    try {
      // Context7 MCP: Set sync in progress flag
      _isSyncInProgress = true;

      // Context7 MCP: Check if incremental sync is possible
      final shouldPerformIncrementalSync =
          _backgroundSyncConfig['incremental_sync_enabled'] as bool &&
          _lastSyncTime != null &&
          DateTime.now().difference(_lastSyncTime!).inHours < 24;

      if (shouldPerformIncrementalSync) {
        await _performIncrementalSync(isForegroundSync: isForegroundSync);
      } else {
        await _performFullSync(isForegroundSync: isForegroundSync);
      }

      // Context7 MCP: Update sync timestamps
      final now = DateTime.now();
      _lastSyncTime = now;
      if (!isForegroundSync) {
        _lastBackgroundSyncTime = now;
      }

      debugPrint('UnifiedMasjidsDataManager: Intelligent $syncType sync completed successfully');
    } on Exception catch (error, stackTrace) {
      debugPrint('UnifiedMasjidsDataManager: Intelligent $syncType sync failed: $error');

      // Context7 MCP: Log sync failure with context
      await _logSyncError(error, stackTrace, syncType);
    } finally {
      // Context7 MCP: Always reset sync in progress flag
      _isSyncInProgress = false;
    }
  }

  /// Context7 MCP: Perform incremental sync for efficiency
  ///
  /// Implements delta sync following PowerSync patterns:
  /// - Only sync data that has changed since last sync
  /// - Batch processing for optimal performance
  /// - Priority-based sync for high-importance locations
  Future<void> _performIncrementalSync({required bool isForegroundSync}) async {
    debugPrint('UnifiedMasjidsDataManager: Performing incremental sync (foreground: $isForegroundSync)');

    try {
      // Context7 MCP: Get current data for comparison
      final currentData = state.value ?? <MasjidModel>[];

      // Context7 MCP: Fetch fresh data with enhanced resilience
      final operationKey = isForegroundSync ? _operationKey : _backgroundSyncKey;
      final freshDataResult = await _executeWithEnhancedResilience<List<MasjidModel>>(
        operationKey: operationKey,
        operation: () async {
          final repository = ref.read(masjidsRepositoryProvider);
          final repositoryResult = await repository.getAllMasjids();

          if (repositoryResult.isFailure) {
            throw repositoryResult.errorOrNull ?? const AppError.network('Incremental sync failed');
          }

          final entities = repositoryResult.dataOrNull!;
          return entities.map((entity) => entity.toModel()).toList();
        },
        fallback: () async => currentData, // Fallback to current data
        config: isForegroundSync ? _createCacheResilienceConfig() : _createBackgroundRefreshResilienceConfig(),
      );

      if (freshDataResult.isSuccess) {
        final freshData = freshDataResult.dataOrNull!;

        // Context7 MCP: Check if data has actually changed
        if (!_listsEqual(currentData, freshData)) {
          debugPrint(
            'UnifiedMasjidsDataManager: Incremental sync detected ${freshData.length - currentData.length} changes',
          );

          // Context7 MCP: Update cache and state
          await _cacheResults(freshData);
          state = AsyncValue.data(freshData);

          // Context7 MCP: Trigger cache warming for updated data
          await _triggerCacheWarming(freshData);
        } else {
          debugPrint('UnifiedMasjidsDataManager: Incremental sync - no changes detected');
        }
      }
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Incremental sync failed: $error');
      // Don't update state on incremental sync failure - keep current data
      rethrow;
    }
  }

  /// Context7 MCP: Perform full sync for comprehensive data refresh
  ///
  /// Implements complete data refresh following Context7 MCP patterns:
  /// - Full data reload from repository
  /// - Comprehensive cache invalidation and refresh
  /// - Priority-based sync for high-importance locations
  Future<void> _performFullSync({required bool isForegroundSync}) async {
    debugPrint('UnifiedMasjidsDataManager: Performing full sync (foreground: $isForegroundSync)');

    try {
      // Context7 MCP: Perform full data reload
      final freshData = await _loadMasjidsWithResilience();

      debugPrint('UnifiedMasjidsDataManager: Full sync loaded ${freshData.length} masjids');

      // Context7 MCP: Update state with fresh data
      state = AsyncValue.data(freshData);

      // Context7 MCP: Trigger cache warming for fresh data
      await _triggerCacheWarming(freshData);
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Full sync failed: $error');
      // Don't update state on full sync failure - keep current data
      rethrow;
    }
  }

  /// Context7 MCP: Log sync errors with comprehensive context
  ///
  /// Provides detailed error logging for sync operations following
  /// Context7 MCP best practices for observability and debugging
  Future<void> _logSyncError(Exception error, StackTrace stackTrace, String syncType) async {
    try {
      final errorContext = {
        'sync_type': syncType,
        'last_sync_time': _lastSyncTime?.toIso8601String(),
        'last_background_sync_time': _lastBackgroundSyncTime?.toIso8601String(),
        'sync_in_progress': _isSyncInProgress,
        'background_sync_enabled': _isBackgroundSyncEnabled,
        'current_state_length': state.value?.length ?? 0,
        'error_type': error.runtimeType.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };

      debugPrint('UnifiedMasjidsDataManager: Sync error context: $errorContext');

      // Context7 MCP: Convert to AppError for consistent error handling
      final appError = _convertToAppError(error, stackTrace);
      await _logErrorWithContext(appError, '${_backgroundSyncKey}_error', 'sync_error');
    } on Exception catch (loggingError) {
      debugPrint('UnifiedMasjidsDataManager: Failed to log sync error: $loggingError');
      // Don't let logging errors break sync operations
    }
  }

  /// Context7 MCP: Cache stale data for graceful degradation
  ///
  /// Stores data in a separate stale cache key for fallback purposes
  Future<void> _cacheStaleData(List<MasjidModel> masjids) async {
    try {
      final cacheService = await ref.read(unifiedCacheServiceProvider.future);
      final result = await cacheService.set(
        '${_cacheKey}_stale',
        masjids.map((m) => m.toJson()).toList(),
        expiration: const Duration(days: 7), // Longer expiration for stale data
      );
      if (result.isFailure) {
        debugPrint('UnifiedMasjidsDataManager: Stale cache set failed: ${result.errorOrNull}');
        return;
      }
      debugPrint('UnifiedMasjidsDataManager: Cached ${masjids.length} masjids as stale data');
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Failed to cache stale data: $error');
      // Don't throw - stale caching failure shouldn't break the app
    }
  }

  /// Context7 MCP: Determine adaptive TTL based on data characteristics
  ///
  /// Implements Bentocache-inspired adaptive caching patterns:
  /// - Fresh data gets longer TTL
  /// - Stale data gets shorter TTL for more frequent refresh
  /// - Considers data size and update frequency
  Duration _determineAdaptiveTtl(List<MasjidModel> masjids) {
    try {
      // Context7 MCP: Analyze data characteristics for adaptive TTL
      final dataSize = masjids.length;
      final hasRecentUpdates = _hasRecentUpdates(masjids);
      final isLargeDataset = dataSize > 100;

      if (hasRecentUpdates) {
        // Fresh data - use longer TTL
        return _adaptiveTtlConfig['fresh_data']!;
      } else if (isLargeDataset) {
        // Large dataset - use medium TTL to balance performance and freshness
        return _adaptiveTtlConfig['stale_data']!;
      } else {
        // Small or older dataset - use standard TTL
        return _cacheDuration;
      }
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Failed to determine adaptive TTL: $error');
      // Fallback to default duration
      return _cacheDuration;
    }
  }

  /// Context7 MCP: Check if data has recent updates
  ///
  /// Analyzes masjid data to determine freshness for adaptive TTL
  bool _hasRecentUpdates(List<MasjidModel> masjids) {
    try {
      // Check data completeness as a proxy for freshness
      // Complete data suggests recent updates or maintenance
      final completeDataCount = masjids
          .where((masjid) => (masjid.officialNameEn?.isNotEmpty ?? false) && (masjid.addressEn?.isNotEmpty ?? false))
          .length;

      // Consider data fresh if most records are complete
      final completenessRatio = completeDataCount / masjids.length;
      return completenessRatio > 0.8;
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Failed to check recent updates: $error');
      return false;
    }
  }

  /// Context7 MCP: Fallback to basic caching when advanced features fail
  ///
  /// Provides graceful degradation when advanced caching features are unavailable
  Future<void> _fallbackBasicCaching(List<MasjidModel> masjids) async {
    try {
      final cacheService = await ref.read(unifiedCacheServiceProvider.future);
      final result = await cacheService.set(
        _cacheKey,
        masjids.map((m) => m.toJson()).toList(),
        expiration: _cacheDuration,
      );
      if (result.isFailure) {
        debugPrint('UnifiedMasjidsDataManager: Basic cache fallback failed: ${result.errorOrNull}');
        return;
      }
      debugPrint('UnifiedMasjidsDataManager: Fallback cached ${masjids.length} masjids with basic TTL');
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Basic cache fallback error: $error');
      // Don't throw - even fallback caching failure shouldn't break the app
    }
  }

  /// Context7 MCP: Trigger cache warming for related data
  ///
  /// Implements Bentocache-inspired cache warming patterns:
  /// - Preloads related governorate and city data
  /// - Warms frequently accessed masjid subsets
  /// - Schedules background refresh for popular locations
  Future<void> _triggerCacheWarming(List<MasjidModel> masjids) async {
    try {
      if (!(_cacheWarmingConfig['enabled'] as bool)) {
        return;
      }

      // Context7 MCP: Extract unique governorates and cities for warming
      final governorateIds = <String>{};
      final cityIds = <String>{};

      for (final masjid in masjids) {
        if (masjid.govId?.isNotEmpty ?? false) {
          governorateIds.add(masjid.govId!);
        }
        if (masjid.cityId?.isNotEmpty ?? false) {
          cityIds.add(masjid.cityId!);
        }
      }

      // Context7 MCP: Warm cache for popular governorates
      final popularGovs = _cacheWarmingConfig['preload_governorates'] as List<String>;
      for (final govId in popularGovs) {
        if (governorateIds.contains(govId)) {
          await _warmGovernorateCache(govId, masjids);
        }
      }

      debugPrint(
        'UnifiedMasjidsDataManager: Cache warming triggered for ${governorateIds.length} governorates, ${cityIds.length} cities',
      );
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Cache warming failed: $error');
      // Don't throw - cache warming failure shouldn't break the app
    }
  }

  /// Context7 MCP: Warm cache for specific governorate
  ///
  /// Preloads masjid data for a specific governorate to improve performance
  Future<void> _warmGovernorateCache(String govId, List<MasjidModel> allMasjids) async {
    try {
      final govMasjids = allMasjids.where((masjid) => masjid.govId == govId).toList();
      if (govMasjids.isEmpty) return;

      final cacheService = await ref.read(unifiedCacheServiceProvider.future);
      final warmingKey = '$_cacheNamespace:gov_$govId';

      final result = await cacheService.set(
        warmingKey,
        govMasjids.map((m) => m.toJson()).toList(),
        expiration: _adaptiveTtlConfig['preload_data']!,
      );

      if (result.isSuccess) {
        debugPrint('UnifiedMasjidsDataManager: Warmed cache for governorate $govId with ${govMasjids.length} masjids');
      }
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Failed to warm governorate cache for $govId: $error');
    }
  }

  /// Helper method to compare lists for changes
  bool _listsEqual(List<MasjidModel> list1, List<MasjidModel> list2) {
    if (list1.length != list2.length) return false;

    for (var i = 0; i < list1.length; i++) {
      if (list1[i].id != list2[i].id) return false;
    }

    return true;
  }

  /// Context7 MCP: Public API - Refresh data with optimistic updates
  Future<void> refresh() async {
    debugPrint('UnifiedMasjidsDataManager: Manual refresh requested');

    // Set loading state
    state = const AsyncValue.loading();

    // Reload data
    state = await AsyncValue.guard(() => _loadMasjidsWithResilience());
  }

  /// Context7 MCP: Public API - Load masjids by governorate
  Future<void> loadByGovernorate(String govId) async {
    debugPrint('UnifiedMasjidsDataManager: Loading masjids for governorate: $govId');

    state = const AsyncValue.loading();

    try {
      final repository = ref.read(masjidsRepositoryProvider);
      final result = await repository.getMasjidsByGovernorate(govId);

      if (result.isFailure) {
        throw result.errorOrNull ?? const AppError.network('Failed to load masjids for governorate');
      }

      // Convert domain entities to models
      final entities = result.dataOrNull!;
      final masjids = entities.map((entity) => entity.toModel()).toList();
      state = AsyncValue.data(masjids);
    } on Exception catch (error, stackTrace) {
      debugPrint('UnifiedMasjidsDataManager: Error loading by governorate: $error');
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Context7 MCP: Public API - Load masjids by city
  Future<void> loadByCity(String cityId) async {
    debugPrint('UnifiedMasjidsDataManager: Loading masjids for city: $cityId');

    state = const AsyncValue.loading();

    try {
      final repository = ref.read(masjidsRepositoryProvider);
      final result = await repository.getMasjidsByCity(cityId);

      if (result.isFailure) {
        throw result.errorOrNull ?? const AppError.network('Failed to load masjids for city');
      }

      // Convert domain entities to models
      final entities = result.dataOrNull!;
      final masjids = entities.map((entity) => entity.toModel()).toList();
      state = AsyncValue.data(masjids);
    } on Exception catch (error, stackTrace) {
      debugPrint('UnifiedMasjidsDataManager: Error loading by city: $error');
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// Context7 MCP: Public API - Reset to all masjids
  Future<void> resetToAll() async {
    debugPrint('UnifiedMasjidsDataManager: Resetting to all masjids');
    await refresh();
  }

  /// Context7 MCP: Public API - Invalidate cache by tags
  ///
  /// Implements Bentocache-inspired tag-based invalidation patterns
  /// for selective cache clearing based on logical groupings
  Future<void> invalidateByTags(List<String> tags) async {
    try {
      final cacheService = await ref.read(unifiedCacheServiceProvider.future);

      // Context7 MCP: Invalidate cache entries by tags using clear method
      final result = await cacheService.clear(tags: tags);

      if (result.isSuccess) {
        debugPrint('UnifiedMasjidsDataManager: Successfully invalidated cache by tags: $tags');
        // Trigger refresh to reload data
        await refresh();
      } else {
        debugPrint('UnifiedMasjidsDataManager: Failed to invalidate cache by tags: ${result.errorOrNull}');
      }
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Error invalidating cache by tags: $error');
    }
  }

  /// Context7 MCP: Public API - Invalidate all masjids cache
  ///
  /// Clears all masjids-related cache entries including:
  /// - Main cache
  /// - Stale cache
  /// - Governorate-specific caches
  /// - City-specific caches
  Future<void> invalidateAllCache() async {
    try {
      await invalidateByTags(_cacheTags);
      debugPrint('UnifiedMasjidsDataManager: All masjids cache invalidated');
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Error invalidating all cache: $error');
    }
  }

  /// Context7 MCP: Public API - Warm cache for specific governorate
  ///
  /// Preloads and caches data for a specific governorate to improve performance
  Future<void> warmCacheForGovernorate(String govId) async {
    try {
      final currentData = state.value;
      if (currentData != null) {
        await _warmGovernorateCache(govId, currentData);
        debugPrint('UnifiedMasjidsDataManager: Cache warmed for governorate: $govId');
      } else {
        debugPrint('UnifiedMasjidsDataManager: No data available for cache warming');
      }
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Error warming cache for governorate $govId: $error');
    }
  }

  /// Context7 MCP: Public API - Get cache statistics
  ///
  /// Returns information about cache usage and performance
  Future<Map<String, dynamic>> getCacheStatistics() async {
    try {
      final cacheService = await ref.read(unifiedCacheServiceProvider.future);
      final statsResult = await cacheService.getStatistics();

      if (statsResult.isSuccess) {
        final stats = statsResult.valueOrNull!;
        return {
          'cache_hits': stats.hitCount,
          'cache_misses': stats.missCount,
          'cache_size': stats.totalSizeBytes,
          'cache_entries': stats.totalEntries,
          'hit_ratio': stats.hitRate,
          'miss_ratio': stats.missRate,
          'memory_entries': stats.memoryEntries,
          'memory_size': stats.memorySize,
          'persistent_entries': stats.persistentEntries,
          'compression_savings': stats.compressionSavings,
          'eviction_count': stats.evictionCount,
          'namespace': _cacheNamespace,
          'tags': _cacheTags,
          'adaptive_ttl_config': _adaptiveTtlConfig,
          'warming_config': _cacheWarmingConfig,
        };
      } else {
        return {'error': 'Failed to get cache statistics', 'namespace': _cacheNamespace, 'tags': _cacheTags};
      }
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Error getting cache statistics: $error');
      return {'error': error.toString(), 'namespace': _cacheNamespace, 'tags': _cacheTags};
    }
  }

  /// Context7 MCP: Public API - Enable/disable background sync
  ///
  /// Allows dynamic control of background sync functionality
  void setBackgroundSyncEnabled(bool enabled) {
    if (_isBackgroundSyncEnabled == enabled) return;

    _isBackgroundSyncEnabled = enabled;
    debugPrint('UnifiedMasjidsDataManager: Background sync ${enabled ? 'enabled' : 'disabled'}');

    if (enabled) {
      _initializeBackgroundSync();
    } else {
      _stopBackgroundSync();
    }
  }

  /// Context7 MCP: Public API - Force immediate sync
  ///
  /// Triggers immediate sync regardless of timers
  Future<void> forceSync({bool incremental = false}) async {
    debugPrint('UnifiedMasjidsDataManager: Force sync requested (incremental: $incremental)');

    if (_isSyncInProgress) {
      debugPrint('UnifiedMasjidsDataManager: Sync already in progress, skipping force sync');
      return;
    }

    try {
      if (incremental && _lastSyncTime != null) {
        await _performIncrementalSync(isForegroundSync: true);
      } else {
        await _performFullSync(isForegroundSync: true);
      }
    } on Exception catch (error) {
      debugPrint('UnifiedMasjidsDataManager: Force sync failed: $error');
      rethrow;
    }
  }

  /// Context7 MCP: Public API - Get sync status
  ///
  /// Returns current sync state and timing information
  Map<String, dynamic> getSyncStatus() {
    return {
      'sync_in_progress': _isSyncInProgress,
      'background_sync_enabled': _isBackgroundSyncEnabled,
      'last_sync_time': _lastSyncTime?.toIso8601String(),
      'last_background_sync_time': _lastBackgroundSyncTime?.toIso8601String(),
      'foreground_timer_active': _foregroundSyncTimer?.isActive ?? false,
      'background_timer_active': _backgroundSyncTimer?.isActive ?? false,
      'sync_interval_minutes': (_backgroundSyncConfig['sync_interval'] as Duration).inMinutes,
      'background_sync_interval_hours': (_backgroundSyncConfig['background_sync_interval'] as Duration).inHours,
    };
  }

  /// Context7 MCP: Stop background sync timers
  ///
  /// Cancels all background sync timers for cleanup
  void _stopBackgroundSync() {
    _foregroundSyncTimer?.cancel();
    _backgroundSyncTimer?.cancel();
    _foregroundSyncTimer = null;
    _backgroundSyncTimer = null;
    debugPrint('UnifiedMasjidsDataManager: Background sync timers stopped');
  }

  /// Context7 MCP: Cleanup resources on disposal
  ///
  /// Ensures proper cleanup of timers and resources
  void dispose() {
    _stopBackgroundSync();
    debugPrint('UnifiedMasjidsDataManager: Resources disposed');
  }

  // Note: Public getters removed as per Riverpod best practices
  // Access data through state.value, loading through state.isLoading, etc.
}

// Cache invalidation provider will be implemented later when needed
