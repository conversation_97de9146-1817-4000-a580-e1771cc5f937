import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/permission_models.dart';
import '../providers/unified_notification_provider.dart';

/// Graceful Degradation Service
///
/// **Task 3.3.5: Implement graceful degradation for denied permissions**
///
/// This service implements Context7 MCP patterns for graceful degradation when
/// notification permissions are denied, providing alternative functionality
/// and maintaining user experience quality.
///
/// Features:
/// - Intelligent fallback strategies for denied permissions
/// - Alternative notification delivery methods
/// - User experience preservation with degraded functionality
/// - Progressive enhancement based on available permissions
/// - Contextual messaging and guidance for users
/// - Automatic adaptation to permission changes
/// - Performance optimization for limited functionality
/// - Accessibility support for all degradation modes
class GracefulDegradationService {
  final WidgetRef ref;
  final Map<PermissionNotificationType, DegradationStrategy> _strategies = {};
  final StreamController<DegradationEvent> _eventController = StreamController.broadcast();

  GracefulDegradationService({required this.ref}) {
    _initializeStrategies();
  }

  /// Stream of degradation events
  Stream<DegradationEvent> get degradationEvents => _eventController.stream;

  /// Initialize degradation strategies for each permission type
  ///
  /// **Context7 MCP Implementation:**
  /// - Single responsibility: Each strategy handles one permission type
  /// - Open/closed principle: New strategies can be added without modifying existing code
  /// - Dependency inversion: Strategies depend on abstractions, not concrete implementations
  /// - Interface segregation: Each strategy implements only required methods
  ///
  /// **Usage:**
  /// ```dart
  /// final service = GracefulDegradationService(ref: ref);
  /// await service.handleDeniedPermissions([PermissionNotificationType.local]);
  /// ```
  void _initializeStrategies() {
    // Local notification degradation strategy
    _strategies[PermissionNotificationType.local] = LocalNotificationDegradationStrategy();

    // Push notification degradation strategy
    _strategies[PermissionNotificationType.push] = PushNotificationDegradationStrategy();

    // Background notification degradation strategy
    _strategies[PermissionNotificationType.background] = BackgroundNotificationDegradationStrategy();

    // Critical alert degradation strategy
    _strategies[PermissionNotificationType.critical] = CriticalAlertDegradationStrategy();

    // Scheduled notification degradation strategy
    _strategies[PermissionNotificationType.scheduled] = ScheduledNotificationDegradationStrategy();

    // Provisional notification degradation strategy
    _strategies[PermissionNotificationType.provisional] = ProvisionalNotificationDegradationStrategy();
  }

  /// Handle denied permissions with graceful degradation
  Future<DegradationResult> handleDeniedPermissions(
    List<PermissionNotificationType> deniedTypes, {
    bool enableFallbacks = true,
    bool showUserGuidance = true,
    bool adaptInterface = true,
    DegradationLevel level = DegradationLevel.comprehensive,
  }) async {
    try {
      final results = <PermissionNotificationType, DegradationStrategyResult>{};
      final fallbacks = <AlternativeMethod>[];
      final userMessages = <UserMessage>[];
      final interfaceAdaptations = <InterfaceAdaptation>[];

      // Process each denied permission type
      for (final type in deniedTypes) {
        final strategy = _strategies[type];
        if (strategy != null) {
          // Apply degradation strategy
          final result = await strategy.applyDegradation(
            context: DegradationContext(
              deniedType: type,
              enableFallbacks: enableFallbacks,
              showUserGuidance: showUserGuidance,
              adaptInterface: adaptInterface,
              level: level,
            ),
          );

          results[type] = result;
          fallbacks.addAll(result.alternativeMethods);
          userMessages.addAll(result.userMessages);
          interfaceAdaptations.addAll(result.interfaceAdaptations);

          // Emit degradation event
          _eventController.add(
            DegradationEvent(type: type, strategy: strategy.strategyType, result: result, timestamp: DateTime.now()),
          );
        }
      }

      // Create comprehensive degradation result
      final degradationResult = DegradationResult(
        deniedTypes: deniedTypes,
        strategyResults: results,
        alternativeMethods: _consolidateAlternativeMethods(fallbacks),
        userMessages: _consolidateUserMessages(userMessages),
        interfaceAdaptations: _consolidateInterfaceAdaptations(interfaceAdaptations),
        overallImpact: _calculateOverallImpact(results),
        recommendedActions: _generateRecommendedActions(results),
        degradationLevel: level,
        appliedAt: DateTime.now(),
      );

      // Apply interface adaptations if requested
      if (adaptInterface) {
        await _applyInterfaceAdaptations(degradationResult.interfaceAdaptations);
      }

      return degradationResult;
    } catch (e, stackTrace) {
      throw DegradationException('Failed to apply graceful degradation: $e', stackTrace: stackTrace);
    }
  }

  /// Get current degradation status
  Future<DegradationStatus> getCurrentDegradationStatus() async {
    try {
      // Check current permission status
      final permissionStatus = await ref.read(unifiedNotificationSettingsProvider.notifier).getPermissionStatusReport();

      final deniedTypes = permissionStatus.permissionCheck.deniedPermissions;
      final permanentlyDeniedTypes = permissionStatus.permissionCheck.permanentlyDeniedPermissions;

      // Analyze current degradation state
      final activeDegradations = <PermissionNotificationType, DegradationInfo>{};

      for (final type in [...deniedTypes, ...permanentlyDeniedTypes]) {
        final strategy = _strategies[type];
        if (strategy != null) {
          activeDegradations[type] = DegradationInfo(
            type: type,
            strategy: strategy.strategyType,
            isPermanent: permanentlyDeniedTypes.contains(type),
            impact: strategy.getImpactLevel(),
            alternativesAvailable: strategy.hasAlternatives(),
          );
        }
      }

      return DegradationStatus(
        activeDegradations: activeDegradations,
        overallImpact: _calculateOverallImpactFromInfo(activeDegradations.values.toList()),
        hasActiveDegradations: activeDegradations.isNotEmpty,
        hasPermanentDegradations: permanentlyDeniedTypes.isNotEmpty,
        statusTime: DateTime.now(),
      );
    } catch (e, stackTrace) {
      throw DegradationException('Failed to get degradation status: $e', stackTrace: stackTrace);
    }
  }

  /// Get available alternative methods for denied permissions
  List<AlternativeMethod> getAlternativeMethods(List<PermissionNotificationType> deniedTypes) {
    final alternatives = <AlternativeMethod>[];

    for (final type in deniedTypes) {
      final strategy = _strategies[type];
      if (strategy != null) {
        alternatives.addAll(strategy.getAlternativeMethods());
      }
    }

    return _consolidateAlternativeMethods(alternatives);
  }

  /// Get user guidance for degraded functionality
  List<UserMessage> getUserGuidance(List<PermissionNotificationType> deniedTypes) {
    final messages = <UserMessage>[];

    for (final type in deniedTypes) {
      final strategy = _strategies[type];
      if (strategy != null) {
        messages.addAll(strategy.getUserMessages());
      }
    }

    return _consolidateUserMessages(messages);
  }

  /// Adapt interface based on denied permissions
  Future<void> adaptInterface(List<PermissionNotificationType> deniedTypes) async {
    final adaptations = <InterfaceAdaptation>[];

    for (final type in deniedTypes) {
      final strategy = _strategies[type];
      if (strategy != null) {
        adaptations.addAll(strategy.getInterfaceAdaptations());
      }
    }

    await _applyInterfaceAdaptations(_consolidateInterfaceAdaptations(adaptations));
  }

  /// Check if functionality is available with current permissions
  Future<bool> isFunctionalityAvailable(NotificationFunctionality functionality) async {
    try {
      final permissionStatus = await ref.read(unifiedNotificationSettingsProvider.notifier).getPermissionStatusReport();

      final requiredPermissions = _getRequiredPermissions(functionality);
      final grantedPermissions = permissionStatus.permissionCheck.grantedPermissions;

      // Check if all required permissions are granted
      final hasAllRequired = requiredPermissions.every((required) => grantedPermissions.contains(required));

      if (hasAllRequired) {
        return true;
      }

      // Check if alternatives are available
      final deniedRequired = requiredPermissions.where((required) => !grantedPermissions.contains(required)).toList();

      return _hasAlternativesForFunctionality(functionality, deniedRequired);
    } catch (e) {
      return false;
    }
  }

  /// Get degradation recommendations
  List<DegradationRecommendation> getDegradationRecommendations(List<PermissionNotificationType> deniedTypes) {
    final recommendations = <DegradationRecommendation>[];

    for (final type in deniedTypes) {
      final strategy = _strategies[type];
      if (strategy != null) {
        recommendations.addAll(strategy.getRecommendations());
      }
    }

    // Sort by priority and remove duplicates
    recommendations.sort((a, b) => a.priority.compareTo(b.priority));
    return recommendations.toSet().toList();
  }

  /// Monitor permission changes and adapt degradation
  void startDegradationMonitoring() {
    // Listen to permission changes from the unified provider
    ref.listen(unifiedNotificationSettingsProvider, (previous, next) {
      if (previous != null && next.hasValue) {
        _handlePermissionChanges(previous, next.value!);
      }
    });
  }

  /// Stop degradation monitoring
  void stopDegradationMonitoring() {
    // Implementation would stop monitoring
    // For now, this is a placeholder
  }

  /// Dispose resources
  void dispose() {
    _eventController.close();
  }

  // ============================================================================
  // PRIVATE HELPER METHODS
  // ============================================================================

  /// Handle permission changes and adapt degradation
  Future<void> _handlePermissionChanges(
    AsyncValue<UnifiedNotificationSettings> previous,
    UnifiedNotificationSettings current,
  ) async {
    try {
      // Get current permission status
      final currentStatus = await ref.read(unifiedNotificationSettingsProvider.notifier).getPermissionStatusReport();

      // Check for newly denied permissions
      final newlyDenied = <PermissionNotificationType>[];
      final newlyGranted = <PermissionNotificationType>[];

      // This would compare previous and current permissions
      // For now, we'll work with current denied permissions
      final deniedTypes = currentStatus.permissionCheck.deniedPermissions;

      if (deniedTypes.isNotEmpty) {
        // Apply degradation for newly denied permissions
        await handleDeniedPermissions(deniedTypes);
      }

      // Handle newly granted permissions (restore functionality)
      if (newlyGranted.isNotEmpty) {
        await _restoreFunctionality(newlyGranted);
      }
    } catch (e) {
      debugPrint('Error handling permission changes: $e');
    }
  }

  /// Restore functionality for newly granted permissions
  Future<void> _restoreFunctionality(List<PermissionNotificationType> grantedTypes) async {
    for (final type in grantedTypes) {
      final strategy = _strategies[type];
      if (strategy != null) {
        await strategy.restoreFunctionality();

        _eventController.add(
          DegradationEvent(
            type: type,
            strategy: strategy.strategyType,
            result: DegradationStrategyResult.restored(),
            timestamp: DateTime.now(),
          ),
        );
      }
    }
  }

  /// Consolidate alternative methods
  List<AlternativeMethod> _consolidateAlternativeMethods(List<AlternativeMethod> methods) {
    final consolidated = <String, AlternativeMethod>{};

    for (final method in methods) {
      final existing = consolidated[method.id];
      if (existing == null || method.priority < existing.priority) {
        consolidated[method.id] = method;
      }
    }

    final result = consolidated.values.toList();
    result.sort((a, b) => a.priority.compareTo(b.priority));
    return result;
  }

  /// Consolidate user messages
  List<UserMessage> _consolidateUserMessages(List<UserMessage> messages) {
    final consolidated = <String, UserMessage>{};

    for (final message in messages) {
      final existing = consolidated[message.id];
      if (existing == null || message.priority < existing.priority) {
        consolidated[message.id] = message;
      }
    }

    final result = consolidated.values.toList();
    result.sort((a, b) => a.priority.compareTo(b.priority));
    return result;
  }

  /// Consolidate interface adaptations
  List<InterfaceAdaptation> _consolidateInterfaceAdaptations(List<InterfaceAdaptation> adaptations) {
    final consolidated = <String, InterfaceAdaptation>{};

    for (final adaptation in adaptations) {
      final existing = consolidated[adaptation.id];
      if (existing == null || adaptation.priority < existing.priority) {
        consolidated[adaptation.id] = adaptation;
      }
    }

    final result = consolidated.values.toList();
    result.sort((a, b) => a.priority.compareTo(b.priority));
    return result;
  }

  /// Calculate overall impact from strategy results
  DegradationImpact _calculateOverallImpact(Map<PermissionNotificationType, DegradationStrategyResult> results) {
    if (results.isEmpty) return DegradationImpact.none;

    final impacts = results.values.map((result) => result.impact).toList();

    if (impacts.contains(DegradationImpact.severe)) {
      return DegradationImpact.severe;
    } else if (impacts.contains(DegradationImpact.moderate)) {
      return DegradationImpact.moderate;
    } else if (impacts.contains(DegradationImpact.minor)) {
      return DegradationImpact.minor;
    } else {
      return DegradationImpact.none;
    }
  }

  /// Calculate overall impact from degradation info
  DegradationImpact _calculateOverallImpactFromInfo(List<DegradationInfo> infos) {
    if (infos.isEmpty) return DegradationImpact.none;

    final impacts = infos.map((info) => info.impact).toList();

    if (impacts.contains(DegradationImpact.severe)) {
      return DegradationImpact.severe;
    } else if (impacts.contains(DegradationImpact.moderate)) {
      return DegradationImpact.moderate;
    } else if (impacts.contains(DegradationImpact.minor)) {
      return DegradationImpact.minor;
    } else {
      return DegradationImpact.none;
    }
  }

  /// Generate recommended actions
  List<String> _generateRecommendedActions(Map<PermissionNotificationType, DegradationStrategyResult> results) {
    final actions = <String>[];

    for (final entry in results.entries) {
      final type = entry.key;
      final result = entry.value;

      switch (result.impact) {
        case DegradationImpact.severe:
          actions.add('Consider requesting ${_getTypeDisplayName(type)} permission again');
          break;
        case DegradationImpact.moderate:
          actions.add('Review alternative methods for ${_getTypeDisplayName(type)}');
          break;
        case DegradationImpact.minor:
          actions.add('${_getTypeDisplayName(type)} functionality is limited but usable');
          break;
        case DegradationImpact.none:
          break;
      }
    }

    return actions;
  }

  /// Apply interface adaptations
  Future<void> _applyInterfaceAdaptations(List<InterfaceAdaptation> adaptations) async {
    for (final adaptation in adaptations) {
      try {
        await adaptation.apply();
      } catch (e) {
        debugPrint('Failed to apply interface adaptation ${adaptation.id}: $e');
      }
    }
  }

  /// Get required permissions for functionality
  List<PermissionNotificationType> _getRequiredPermissions(NotificationFunctionality functionality) {
    switch (functionality) {
      case NotificationFunctionality.prayerAlerts:
        return [PermissionNotificationType.local, PermissionNotificationType.scheduled];
      case NotificationFunctionality.communityUpdates:
        return [PermissionNotificationType.push];
      case NotificationFunctionality.backgroundSync:
        return [PermissionNotificationType.background];
      case NotificationFunctionality.criticalAlerts:
        return [PermissionNotificationType.critical];
      case NotificationFunctionality.quietNotifications:
        return [PermissionNotificationType.provisional];
      case NotificationFunctionality.allNotifications:
        return PermissionNotificationType.values;
    }
  }

  /// Check if alternatives are available for functionality
  bool _hasAlternativesForFunctionality(
    NotificationFunctionality functionality,
    List<PermissionNotificationType> deniedTypes,
  ) {
    // Check if any of the denied types have alternatives
    return deniedTypes.any((type) {
      final strategy = _strategies[type];
      return strategy?.hasAlternatives() ?? false;
    });
  }

  /// Get display name for permission type
  String _getTypeDisplayName(PermissionNotificationType type) {
    switch (type) {
      case PermissionNotificationType.local:
        return 'Local Notifications';
      case PermissionNotificationType.push:
        return 'Push Notifications';
      case PermissionNotificationType.scheduled:
        return 'Scheduled Notifications';
      case PermissionNotificationType.background:
        return 'Background Notifications';
      case PermissionNotificationType.critical:
        return 'Critical Alerts';
      case PermissionNotificationType.provisional:
        return 'Provisional Notifications';
    }
  }
}

// ============================================================================
// DEGRADATION STRATEGIES
// ============================================================================

/// Base degradation strategy
abstract class DegradationStrategy {
  DegradationStrategyType get strategyType;

  Future<DegradationStrategyResult> applyDegradation(DegradationContext context);
  Future<void> restoreFunctionality();

  List<AlternativeMethod> getAlternativeMethods();
  List<UserMessage> getUserMessages();
  List<InterfaceAdaptation> getInterfaceAdaptations();
  List<DegradationRecommendation> getRecommendations();

  DegradationImpact getImpactLevel();
  bool hasAlternatives();
}

/// Local notification degradation strategy
class LocalNotificationDegradationStrategy extends DegradationStrategy {
  @override
  DegradationStrategyType get strategyType => DegradationStrategyType.localNotification;

  @override
  Future<DegradationStrategyResult> applyDegradation(DegradationContext context) async {
    return DegradationStrategyResult(
      success: true,
      impact: DegradationImpact.moderate,
      alternativeMethods: [
        const AlternativeMethod(
          id: 'in_app_alerts',
          name: 'In-App Prayer Alerts',
          description: 'Visual and audio alerts when the app is open',
          priority: 1,
          effectiveness: 0.7,
        ),
        const AlternativeMethod(
          id: 'widget_display',
          name: 'Home Screen Widget',
          description: 'Display prayer times on home screen widget',
          priority: 2,
          effectiveness: 0.5,
        ),
      ],
      userMessages: [
        const UserMessage(
          id: 'local_notification_denied',
          title: 'Prayer Alerts Limited',
          message:
              'Prayer time alerts will only work when the app is open. Consider enabling notifications for better experience.',
          type: UserMessageType.information,
          priority: 1,
        ),
      ],
      interfaceAdaptations: [
        InterfaceAdaptation(
          id: 'show_in_app_alerts',
          name: 'Enable In-App Alerts',
          description: 'Show prominent in-app prayer time alerts',
          priority: 1,
          apply: () async {
            // Implementation would enable in-app alerts
          },
        ),
      ],
    );
  }

  @override
  Future<void> restoreFunctionality() async {
    // Restore full local notification functionality
  }

  @override
  List<AlternativeMethod> getAlternativeMethods() {
    return [
      const AlternativeMethod(
        id: 'in_app_alerts',
        name: 'In-App Prayer Alerts',
        description: 'Visual and audio alerts when the app is open',
        priority: 1,
        effectiveness: 0.7,
      ),
    ];
  }

  @override
  List<UserMessage> getUserMessages() {
    return [
      const UserMessage(
        id: 'local_notification_guidance',
        title: 'Enable Notifications',
        message:
            'To receive prayer time alerts even when the app is closed, please enable notifications in your device settings.',
        type: UserMessageType.guidance,
        priority: 1,
      ),
    ];
  }

  @override
  List<InterfaceAdaptation> getInterfaceAdaptations() {
    return [
      InterfaceAdaptation(
        id: 'highlight_in_app_features',
        name: 'Highlight In-App Features',
        description: 'Emphasize features that work without notifications',
        priority: 1,
        apply: () async {
          // Implementation would highlight in-app features
        },
      ),
    ];
  }

  @override
  List<DegradationRecommendation> getRecommendations() {
    return [
      const DegradationRecommendation(
        id: 'enable_local_notifications',
        title: 'Enable Local Notifications',
        description: 'Allow the app to send prayer time alerts',
        priority: 1,
        impact: DegradationImpact.moderate,
      ),
    ];
  }

  @override
  DegradationImpact getImpactLevel() => DegradationImpact.moderate;

  @override
  bool hasAlternatives() => true;
}

/// Push notification degradation strategy
class PushNotificationDegradationStrategy extends DegradationStrategy {
  @override
  DegradationStrategyType get strategyType => DegradationStrategyType.pushNotification;

  @override
  Future<DegradationStrategyResult> applyDegradation(DegradationContext context) async {
    return DegradationStrategyResult(
      success: true,
      impact: DegradationImpact.minor,
      alternativeMethods: [
        const AlternativeMethod(
          id: 'manual_refresh',
          name: 'Manual Content Refresh',
          description: 'Pull to refresh for latest community updates',
          priority: 1,
          effectiveness: 0.6,
        ),
      ],
      userMessages: [
        const UserMessage(
          id: 'push_notification_denied',
          title: 'Community Updates Limited',
          message: 'You won\'t receive automatic community updates. Pull to refresh for the latest content.',
          type: UserMessageType.information,
          priority: 2,
        ),
      ],
      interfaceAdaptations: [
        InterfaceAdaptation(
          id: 'add_refresh_indicators',
          name: 'Add Refresh Indicators',
          description: 'Show pull-to-refresh indicators prominently',
          priority: 2,
          apply: () async {
            // Implementation would add refresh indicators
          },
        ),
      ],
    );
  }

  @override
  Future<void> restoreFunctionality() async {
    // Restore push notification functionality
  }

  @override
  List<AlternativeMethod> getAlternativeMethods() {
    return [
      const AlternativeMethod(
        id: 'manual_refresh',
        name: 'Manual Content Refresh',
        description: 'Pull to refresh for latest community updates',
        priority: 1,
        effectiveness: 0.6,
      ),
    ];
  }

  @override
  List<UserMessage> getUserMessages() {
    return [
      const UserMessage(
        id: 'push_notification_guidance',
        title: 'Stay Updated',
        message: 'Enable push notifications to receive community updates automatically.',
        type: UserMessageType.guidance,
        priority: 2,
      ),
    ];
  }

  @override
  List<InterfaceAdaptation> getInterfaceAdaptations() {
    return [
      InterfaceAdaptation(
        id: 'emphasize_manual_refresh',
        name: 'Emphasize Manual Refresh',
        description: 'Make refresh actions more prominent',
        priority: 2,
        apply: () async {
          // Implementation would emphasize refresh actions
        },
      ),
    ];
  }

  @override
  List<DegradationRecommendation> getRecommendations() {
    return [
      const DegradationRecommendation(
        id: 'enable_push_notifications',
        title: 'Enable Push Notifications',
        description: 'Receive automatic community updates',
        priority: 2,
        impact: DegradationImpact.minor,
      ),
    ];
  }

  @override
  DegradationImpact getImpactLevel() => DegradationImpact.minor;

  @override
  bool hasAlternatives() => true;
}

/// Background notification degradation strategy
class BackgroundNotificationDegradationStrategy extends DegradationStrategy {
  @override
  DegradationStrategyType get strategyType => DegradationStrategyType.backgroundNotification;

  @override
  Future<DegradationStrategyResult> applyDegradation(DegradationContext context) async {
    return DegradationStrategyResult(
      success: true,
      impact: DegradationImpact.moderate,
      alternativeMethods: [
        const AlternativeMethod(
          id: 'foreground_sync',
          name: 'Foreground Sync',
          description: 'Sync data when app is opened',
          priority: 1,
          effectiveness: 0.8,
        ),
      ],
      userMessages: [
        const UserMessage(
          id: 'background_notification_denied',
          title: 'Background Updates Disabled',
          message: 'The app will sync data when you open it. For real-time updates, enable background notifications.',
          type: UserMessageType.information,
          priority: 1,
        ),
      ],
      interfaceAdaptations: [
        InterfaceAdaptation(
          id: 'show_sync_status',
          name: 'Show Sync Status',
          description: 'Display sync status and last update time',
          priority: 1,
          apply: () async {
            // Implementation would show sync status
          },
        ),
      ],
    );
  }

  @override
  Future<void> restoreFunctionality() async {
    // Restore background notification functionality
  }

  @override
  List<AlternativeMethod> getAlternativeMethods() {
    return [
      const AlternativeMethod(
        id: 'foreground_sync',
        name: 'Foreground Sync',
        description: 'Sync data when app is opened',
        priority: 1,
        effectiveness: 0.8,
      ),
    ];
  }

  @override
  List<UserMessage> getUserMessages() {
    return [
      const UserMessage(
        id: 'background_notification_guidance',
        title: 'Enable Background Updates',
        message: 'Allow background notifications for real-time prayer time updates.',
        type: UserMessageType.guidance,
        priority: 1,
      ),
    ];
  }

  @override
  List<InterfaceAdaptation> getInterfaceAdaptations() {
    return [
      InterfaceAdaptation(
        id: 'show_last_sync',
        name: 'Show Last Sync Time',
        description: 'Display when data was last updated',
        priority: 1,
        apply: () async {
          // Implementation would show last sync time
        },
      ),
    ];
  }

  @override
  List<DegradationRecommendation> getRecommendations() {
    return [
      const DegradationRecommendation(
        id: 'enable_background_notifications',
        title: 'Enable Background Notifications',
        description: 'Allow real-time prayer time updates',
        priority: 1,
        impact: DegradationImpact.moderate,
      ),
    ];
  }

  @override
  DegradationImpact getImpactLevel() => DegradationImpact.moderate;

  @override
  bool hasAlternatives() => true;
}

/// Critical alert degradation strategy
class CriticalAlertDegradationStrategy extends DegradationStrategy {
  @override
  DegradationStrategyType get strategyType => DegradationStrategyType.criticalAlert;

  @override
  Future<DegradationStrategyResult> applyDegradation(DegradationContext context) async {
    return DegradationStrategyResult(
      success: true,
      impact: DegradationImpact.severe,
      alternativeMethods: [
        const AlternativeMethod(
          id: 'regular_notifications',
          name: 'Regular Notifications',
          description: 'Use regular notifications for important alerts',
          priority: 1,
          effectiveness: 0.6,
        ),
      ],
      userMessages: [
        const UserMessage(
          id: 'critical_alert_denied',
          title: 'Critical Alerts Disabled',
          message: 'Important prayer time alerts may not bypass Do Not Disturb mode.',
          type: UserMessageType.warning,
          priority: 1,
        ),
      ],
      interfaceAdaptations: [
        InterfaceAdaptation(
          id: 'emphasize_important_alerts',
          name: 'Emphasize Important Alerts',
          description: 'Make important alerts more prominent in the app',
          priority: 1,
          apply: () async {
            // Implementation would emphasize important alerts
          },
        ),
      ],
    );
  }

  @override
  Future<void> restoreFunctionality() async {
    // Restore critical alert functionality
  }

  @override
  List<AlternativeMethod> getAlternativeMethods() {
    return [
      const AlternativeMethod(
        id: 'regular_notifications',
        name: 'Regular Notifications',
        description: 'Use regular notifications for important alerts',
        priority: 1,
        effectiveness: 0.6,
      ),
    ];
  }

  @override
  List<UserMessage> getUserMessages() {
    return [
      const UserMessage(
        id: 'critical_alert_guidance',
        title: 'Enable Critical Alerts',
        message: 'Allow critical alerts to ensure you never miss important prayer times.',
        type: UserMessageType.guidance,
        priority: 1,
      ),
    ];
  }

  @override
  List<InterfaceAdaptation> getInterfaceAdaptations() {
    return [
      InterfaceAdaptation(
        id: 'highlight_critical_features',
        name: 'Highlight Critical Features',
        description: 'Emphasize critical prayer time features',
        priority: 1,
        apply: () async {
          // Implementation would highlight critical features
        },
      ),
    ];
  }

  @override
  List<DegradationRecommendation> getRecommendations() {
    return [
      const DegradationRecommendation(
        id: 'enable_critical_alerts',
        title: 'Enable Critical Alerts',
        description: 'Never miss important prayer times',
        priority: 1,
        impact: DegradationImpact.severe,
      ),
    ];
  }

  @override
  DegradationImpact getImpactLevel() => DegradationImpact.severe;

  @override
  bool hasAlternatives() => true;
}

/// Scheduled notification degradation strategy
class ScheduledNotificationDegradationStrategy extends DegradationStrategy {
  @override
  DegradationStrategyType get strategyType => DegradationStrategyType.scheduledNotification;

  @override
  Future<DegradationStrategyResult> applyDegradation(DegradationContext context) async {
    return DegradationStrategyResult(
      success: true,
      impact: DegradationImpact.moderate,
      alternativeMethods: [
        const AlternativeMethod(
          id: 'approximate_timing',
          name: 'Approximate Timing',
          description: 'Use approximate timing for prayer alerts',
          priority: 1,
          effectiveness: 0.7,
        ),
      ],
      userMessages: [
        const UserMessage(
          id: 'scheduled_notification_denied',
          title: 'Precise Timing Limited',
          message: 'Prayer time alerts may not be precisely timed. Enable exact alarms for better accuracy.',
          type: UserMessageType.information,
          priority: 1,
        ),
      ],
      interfaceAdaptations: [
        InterfaceAdaptation(
          id: 'show_timing_disclaimer',
          name: 'Show Timing Disclaimer',
          description: 'Inform users about timing limitations',
          priority: 1,
          apply: () async {
            // Implementation would show timing disclaimer
          },
        ),
      ],
    );
  }

  @override
  Future<void> restoreFunctionality() async {
    // Restore scheduled notification functionality
  }

  @override
  List<AlternativeMethod> getAlternativeMethods() {
    return [
      const AlternativeMethod(
        id: 'approximate_timing',
        name: 'Approximate Timing',
        description: 'Use approximate timing for prayer alerts',
        priority: 1,
        effectiveness: 0.7,
      ),
    ];
  }

  @override
  List<UserMessage> getUserMessages() {
    return [
      const UserMessage(
        id: 'scheduled_notification_guidance',
        title: 'Enable Exact Alarms',
        message: 'Allow exact alarms for precise prayer time notifications.',
        type: UserMessageType.guidance,
        priority: 1,
      ),
    ];
  }

  @override
  List<InterfaceAdaptation> getInterfaceAdaptations() {
    return [
      InterfaceAdaptation(
        id: 'show_timing_accuracy',
        name: 'Show Timing Accuracy',
        description: 'Display timing accuracy information',
        priority: 1,
        apply: () async {
          // Implementation would show timing accuracy
        },
      ),
    ];
  }

  @override
  List<DegradationRecommendation> getRecommendations() {
    return [
      const DegradationRecommendation(
        id: 'enable_scheduled_notifications',
        title: 'Enable Exact Alarms',
        description: 'Get precise prayer time notifications',
        priority: 1,
        impact: DegradationImpact.moderate,
      ),
    ];
  }

  @override
  DegradationImpact getImpactLevel() => DegradationImpact.moderate;

  @override
  bool hasAlternatives() => true;
}

// ============================================================================
// DATA MODELS
// ============================================================================

/// Degradation Context
///
/// Context information for applying degradation strategies.
class DegradationContext {
  final PermissionNotificationType deniedType;
  final bool enableFallbacks;
  final bool showUserGuidance;
  final bool adaptInterface;
  final DegradationLevel level;

  const DegradationContext({
    required this.deniedType,
    required this.enableFallbacks,
    required this.showUserGuidance,
    required this.adaptInterface,
    required this.level,
  });
}

/// Degradation Level
enum DegradationLevel { basic, comprehensive, advanced }

/// Degradation Strategy Type
enum DegradationStrategyType {
  localNotification,
  pushNotification,
  backgroundNotification,
  criticalAlert,
  scheduledNotification,
  provisionalNotification,
}

/// Degradation Strategy Result
///
/// Result of applying a degradation strategy.
class DegradationStrategyResult {
  final bool success;
  final DegradationImpact impact;
  final List<AlternativeMethod> alternativeMethods;
  final List<UserMessage> userMessages;
  final List<InterfaceAdaptation> interfaceAdaptations;
  final String? error;

  const DegradationStrategyResult({
    required this.success,
    required this.impact,
    this.alternativeMethods = const [],
    this.userMessages = const [],
    this.interfaceAdaptations = const [],
    this.error,
  });

  /// Create a restored functionality result
  factory DegradationStrategyResult.restored() {
    return const DegradationStrategyResult(success: true, impact: DegradationImpact.none);
  }

  /// Create a failed result
  factory DegradationStrategyResult.failed(String error) {
    return DegradationStrategyResult(success: false, impact: DegradationImpact.severe, error: error);
  }
}

/// Degradation Impact
enum DegradationImpact { none, minor, moderate, severe }

/// Degradation Result
///
/// Comprehensive result of applying graceful degradation.
class DegradationResult {
  final List<PermissionNotificationType> deniedTypes;
  final Map<PermissionNotificationType, DegradationStrategyResult> strategyResults;
  final List<AlternativeMethod> alternativeMethods;
  final List<UserMessage> userMessages;
  final List<InterfaceAdaptation> interfaceAdaptations;
  final DegradationImpact overallImpact;
  final List<String> recommendedActions;
  final DegradationLevel degradationLevel;
  final DateTime appliedAt;

  const DegradationResult({
    required this.deniedTypes,
    required this.strategyResults,
    required this.alternativeMethods,
    required this.userMessages,
    required this.interfaceAdaptations,
    required this.overallImpact,
    required this.recommendedActions,
    required this.degradationLevel,
    required this.appliedAt,
  });

  /// Check if degradation was successful
  bool get wasSuccessful => strategyResults.values.every((result) => result.success);

  /// Get failed strategies
  List<PermissionNotificationType> get failedStrategies {
    return strategyResults.entries.where((entry) => !entry.value.success).map((entry) => entry.key).toList();
  }

  /// Get high-priority user messages
  List<UserMessage> get highPriorityMessages {
    return userMessages.where((message) => message.priority <= 2).toList();
  }

  /// Get critical interface adaptations
  List<InterfaceAdaptation> get criticalAdaptations {
    return interfaceAdaptations.where((adaptation) => adaptation.priority <= 2).toList();
  }
}

/// Degradation Status
///
/// Current status of active degradations.
class DegradationStatus {
  final Map<PermissionNotificationType, DegradationInfo> activeDegradations;
  final DegradationImpact overallImpact;
  final bool hasActiveDegradations;
  final bool hasPermanentDegradations;
  final DateTime statusTime;

  const DegradationStatus({
    required this.activeDegradations,
    required this.overallImpact,
    required this.hasActiveDegradations,
    required this.hasPermanentDegradations,
    required this.statusTime,
  });

  /// Get degradation count
  int get degradationCount => activeDegradations.length;

  /// Get permanent degradation count
  int get permanentDegradationCount {
    return activeDegradations.values.where((info) => info.isPermanent).length;
  }

  /// Get status summary
  String get statusSummary {
    if (!hasActiveDegradations) {
      return 'All permissions granted - full functionality available';
    } else if (hasPermanentDegradations) {
      return '$permanentDegradationCount permanent degradations active';
    } else {
      return '$degradationCount temporary degradations active';
    }
  }
}

/// Degradation Info
///
/// Information about a specific degradation.
class DegradationInfo {
  final PermissionNotificationType type;
  final DegradationStrategyType strategy;
  final bool isPermanent;
  final DegradationImpact impact;
  final bool alternativesAvailable;

  const DegradationInfo({
    required this.type,
    required this.strategy,
    required this.isPermanent,
    required this.impact,
    required this.alternativesAvailable,
  });

  /// Get impact description
  String get impactDescription {
    switch (impact) {
      case DegradationImpact.none:
        return 'No impact on functionality';
      case DegradationImpact.minor:
        return 'Minor reduction in functionality';
      case DegradationImpact.moderate:
        return 'Moderate impact on user experience';
      case DegradationImpact.severe:
        return 'Significant functionality limitations';
    }
  }
}

/// Alternative Method
///
/// Alternative method for denied functionality.
class AlternativeMethod {
  final String id;
  final String name;
  final String description;
  final int priority;
  final double effectiveness;
  final List<String> requirements;
  final Duration? setupTime;

  const AlternativeMethod({
    required this.id,
    required this.name,
    required this.description,
    required this.priority,
    required this.effectiveness,
    this.requirements = const [],
    this.setupTime,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is AlternativeMethod && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  /// Check if method is highly effective
  bool get isHighlyEffective => effectiveness >= 0.8;

  /// Check if method is quick to set up
  bool get isQuickSetup => setupTime != null && setupTime!.inMinutes <= 5;

  /// Get effectiveness percentage
  int get effectivenessPercentage => (effectiveness * 100).round();
}

/// User Message
///
/// Message to display to the user about degraded functionality.
class UserMessage {
  final String id;
  final String title;
  final String message;
  final UserMessageType type;
  final int priority;
  final String? actionText;
  final VoidCallback? action;

  const UserMessage({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.priority,
    this.actionText,
    this.action,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is UserMessage && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  /// Check if message is high priority
  bool get isHighPriority => priority <= 2;

  /// Check if message has an action
  bool get hasAction => action != null && actionText != null;
}

/// User Message Type
enum UserMessageType { information, warning, guidance, error }

/// Interface Adaptation
///
/// Adaptation to the user interface for degraded functionality.
class InterfaceAdaptation {
  final String id;
  final String name;
  final String description;
  final int priority;
  final Future<void> Function() apply;

  const InterfaceAdaptation({
    required this.id,
    required this.name,
    required this.description,
    required this.priority,
    required this.apply,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is InterfaceAdaptation && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  /// Check if adaptation is high priority
  bool get isHighPriority => priority <= 2;
}

/// Degradation Recommendation
///
/// Recommendation for improving degraded functionality.
class DegradationRecommendation {
  final String id;
  final String title;
  final String description;
  final int priority;
  final DegradationImpact impact;
  final String? actionText;
  final VoidCallback? action;

  const DegradationRecommendation({
    required this.id,
    required this.title,
    required this.description,
    required this.priority,
    required this.impact,
    this.actionText,
    this.action,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DegradationRecommendation && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;

  /// Check if recommendation is high priority
  bool get isHighPriority => priority <= 2;

  /// Check if recommendation addresses severe impact
  bool get addressesSevereImpact => impact == DegradationImpact.severe;

  /// Check if recommendation has an action
  bool get hasAction => action != null;
}

/// Degradation Event
///
/// Event emitted when degradation is applied or restored.
class DegradationEvent {
  final PermissionNotificationType type;
  final DegradationStrategyType strategy;
  final DegradationStrategyResult result;
  final DateTime timestamp;

  const DegradationEvent({required this.type, required this.strategy, required this.result, required this.timestamp});

  /// Check if event represents successful degradation
  bool get isSuccessful => result.success;

  /// Check if event represents restoration
  bool get isRestoration => result.impact == DegradationImpact.none;

  /// Get event description
  String get description {
    if (isRestoration) {
      return 'Functionality restored for ${_getTypeDisplayName(type)}';
    } else if (isSuccessful) {
      return 'Graceful degradation applied for ${_getTypeDisplayName(type)}';
    } else {
      return 'Degradation failed for ${_getTypeDisplayName(type)}';
    }
  }

  String _getTypeDisplayName(PermissionNotificationType type) {
    switch (type) {
      case PermissionNotificationType.local:
        return 'Local Notifications';
      case PermissionNotificationType.push:
        return 'Push Notifications';
      case PermissionNotificationType.scheduled:
        return 'Scheduled Notifications';
      case PermissionNotificationType.background:
        return 'Background Notifications';
      case PermissionNotificationType.critical:
        return 'Critical Alerts';
      case PermissionNotificationType.provisional:
        return 'Provisional Notifications';
    }
  }
}

/// Notification Functionality
///
/// Different types of notification functionality.
enum NotificationFunctionality {
  prayerAlerts,
  communityUpdates,
  backgroundSync,
  criticalAlerts,
  quietNotifications,
  allNotifications,
}

/// Degradation Exception
///
/// Exception thrown during degradation operations.
class DegradationException implements Exception {
  final String message;
  final StackTrace? stackTrace;
  final String? code;

  const DegradationException(this.message, {this.stackTrace, this.code});

  @override
  String toString() {
    if (code != null) {
      return 'DegradationException [$code]: $message';
    } else {
      return 'DegradationException: $message';
    }
  }
}

// Import required types (these would normally be imported from actual files)
typedef VoidCallback = void Function();

class UnifiedNotificationSettings {
  // Placeholder for actual implementation
}

/// Provisional notification degradation strategy
class ProvisionalNotificationDegradationStrategy extends DegradationStrategy {
  @override
  DegradationStrategyType get strategyType => DegradationStrategyType.provisionalNotification;

  @override
  Future<DegradationStrategyResult> applyDegradation(DegradationContext context) async {
    return DegradationStrategyResult(
      success: true,
      impact: DegradationImpact.minor,
      alternativeMethods: [
        const AlternativeMethod(
          id: 'regular_notifications',
          name: 'Regular Notifications',
          description: 'Use regular notifications instead of quiet ones',
          priority: 1,
          effectiveness: 0.9,
        ),
      ],
      userMessages: [
        const UserMessage(
          id: 'provisional_notification_denied',
          title: 'Quiet Notifications Disabled',
          message: 'Notifications will be more prominent. Enable provisional notifications for quieter alerts.',
          type: UserMessageType.information,
          priority: 3,
        ),
      ],
      interfaceAdaptations: [
        InterfaceAdaptation(
          id: 'adjust_notification_style',
          name: 'Adjust Notification Style',
          description: 'Use regular notification style',
          priority: 3,
          apply: () async {
            // Implementation would adjust notification style
          },
        ),
      ],
    );
  }

  @override
  Future<void> restoreFunctionality() async {
    // Restore provisional notification functionality
  }

  @override
  List<AlternativeMethod> getAlternativeMethods() {
    return [
      const AlternativeMethod(
        id: 'regular_notifications',
        name: 'Regular Notifications',
        description: 'Use regular notifications instead of quiet ones',
        priority: 1,
        effectiveness: 0.9,
      ),
    ];
  }

  @override
  List<UserMessage> getUserMessages() {
    return [
      const UserMessage(
        id: 'provisional_notification_guidance',
        title: 'Enable Quiet Notifications',
        message: 'Allow provisional notifications for less intrusive alerts.',
        type: UserMessageType.guidance,
        priority: 3,
      ),
    ];
  }

  @override
  List<InterfaceAdaptation> getInterfaceAdaptations() {
    return [
      InterfaceAdaptation(
        id: 'use_regular_style',
        name: 'Use Regular Notification Style',
        description: 'Switch to regular notification presentation',
        priority: 3,
        apply: () async {
          // Implementation would use regular notification style
        },
      ),
    ];
  }

  @override
  List<DegradationRecommendation> getRecommendations() {
    return [
      const DegradationRecommendation(
        id: 'enable_provisional_notifications',
        title: 'Enable Provisional Notifications',
        description: 'Get quieter, less intrusive notifications',
        priority: 3,
        impact: DegradationImpact.minor,
      ),
    ];
  }

  @override
  DegradationImpact getImpactLevel() => DegradationImpact.minor;

  @override
  bool hasAlternatives() => true;
}
